package id.co.bri.brimo.payment.feature.qrshow.ui

import android.view.WindowManager
import androidx.activity.ComponentActivity
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil.compose.AsyncImage
import id.co.bri.brimo.payment.R
import id.co.bri.brimo.payment.app.QrShowRoute
import id.co.bri.brimo.payment.core.common.UiState
import id.co.bri.brimo.payment.core.common.onError
import id.co.bri.brimo.payment.core.common.onSuccess
import id.co.bri.brimo.payment.core.common.serialize
import id.co.bri.brimo.payment.core.design.component.BottomSheet
import id.co.bri.brimo.payment.core.design.component.DividerHorizontal
import id.co.bri.brimo.payment.core.design.component.ErrorBottomSheet
import id.co.bri.brimo.payment.core.design.component.Notice
import id.co.bri.brimo.payment.core.design.component.NoticeType
import id.co.bri.brimo.payment.core.design.component.PrimaryButton
import id.co.bri.brimo.payment.core.design.component.ProgressDialog
import id.co.bri.brimo.payment.core.design.component.Shimmer
import id.co.bri.brimo.payment.core.design.component.SnackbarCustom
import id.co.bri.brimo.payment.core.design.component.SnackbarType
import id.co.bri.brimo.payment.core.design.theme.Color_0054F3
import id.co.bri.brimo.payment.core.design.theme.Color_E84040
import id.co.bri.brimo.payment.core.design.theme.Color_E9EEF6
import id.co.bri.brimo.payment.core.design.theme.MainTheme
import id.co.bri.brimo.payment.core.network.MessageException
import id.co.bri.brimo.payment.core.network.errorSnackbar
import id.co.bri.brimo.payment.core.network.response.base.AccountResponse
import id.co.bri.brimo.payment.feature.brizzi.ui.pin.PinDialog
import id.co.bri.brimo.payment.feature.brizzi.ui.receipt.generateQrCode
import id.co.bri.brimo.payment.feature.qris.ui.base.QrisAccountBottomSheet
import kotlinx.coroutines.delay
import org.koin.androidx.compose.koinViewModel
import java.util.Locale

@Composable
internal fun QrShowScreen(
    navigation: (QrShowNavigation) -> Unit = {},
    qrShowViewModel: QrShowViewModel = koinViewModel()
) {
    val accountList by qrShowViewModel.accountList.collectAsStateWithLifecycle()
    val qrAccountList by qrShowViewModel.qrAccountList.collectAsStateWithLifecycle()
    val qrCpmGenerate by qrShowViewModel.qrCpmGenerate.collectAsStateWithLifecycle()
    val qrCpmCheckStatus by qrShowViewModel.qrCpmCheckStatus.collectAsStateWithLifecycle()

    QrShowContent(
        state = QrShowState(
            qrShowRoute = qrShowViewModel.qrShowRoute,
            accountList = accountList.orEmpty(),
            qrCpmGenerate = qrCpmGenerate,
            qrCpmCheckStatus = qrCpmCheckStatus,
        ),
        event = qrShowViewModel::handleEvent,
        navigation = navigation
    )

    qrAccountList
        .onError { error ->
            ErrorBottomSheet(
                error = error,
                onDismiss = {
                    navigation(QrShowNavigation.Back)
                },
                onClose = {
                    navigation(QrShowNavigation.Back)
                },
                onRetry = {
                    qrShowViewModel.handleEvent(QrShowEvent.RefreshAccountList)
                }
            )
        }
}

@Composable
private fun QrShowContent(
    state: QrShowState,
    event: (QrShowEvent) -> Unit = {},
    navigation: (QrShowNavigation) -> Unit = {}
) {
    val context = LocalContext.current
    val density = LocalDensity.current

    // Global
    var selectedPin by rememberSaveable { mutableStateOf("") }
    var selectedAccount: AccountResponse? by remember { mutableStateOf(null) }
    val notEnough = (selectedAccount?.balance?.toDoubleOrNull() ?: 0.0) < 0.0 &&
        !state.qrShowRoute.fastMenu
    var expireTime by rememberSaveable { mutableIntStateOf(1) }
    val expireText by remember {
        derivedStateOf {
            String.format(
                Locale.getDefault(),
                "%02d:%02d",
                expireTime / 60,
                expireTime % 60
            )
        }
    }
    val expired by remember {
        derivedStateOf {
            expireTime == 0
        }
    }

    // Brightness
    var brightness by rememberSaveable { mutableStateOf(false) }

    DisposableEffect(brightness) {
        val window = (context as? ComponentActivity)?.window
        window?.apply {
            attributes = attributes.apply {
                screenBrightness = if (brightness) 1f else -1f
            }
        }

        onDispose {
            window?.apply {
                attributes = attributes.apply {
                    screenBrightness = -1f
                }
            }
        }
    }

    // Screenshot
    DisposableEffect(Unit) {
        val window = (context as? ComponentActivity)?.window
        window?.addFlags(WindowManager.LayoutParams.FLAG_SECURE)

        onDispose {
            window?.clearFlags(WindowManager.LayoutParams.FLAG_SECURE)
        }
    }

    // Loading
    var progressDialog by rememberSaveable { mutableStateOf(false) }
    if (progressDialog) ProgressDialog()

    // Error
    var errorBottomSheet by rememberSaveable { mutableStateOf(false) }
    var error by remember { mutableStateOf(Throwable()) }
    var retry: QrShowEvent by remember { mutableStateOf(QrShowEvent.CheckStatus) }

    ErrorBottomSheet(
        error = error,
        showBottomSheet = errorBottomSheet,
        onShowBottomSheet = { errorBottomSheet = it },
        onRetry = {
            event(retry)
        }
    )

    // Snackbar
    val snackbarHostState = remember { SnackbarHostState() }
    var snackbarType by rememberSaveable { mutableStateOf(SnackbarType.INFO) }

    // Pin
    var pinDialog by rememberSaveable { mutableStateOf(false) }
    var pinMessage by rememberSaveable { mutableStateOf("") }
    var deletePin by remember { mutableStateOf({}) }

    if (pinDialog) {
        Dialog(
            onDismissRequest = { pinDialog = false },
            properties = DialogProperties(usePlatformDefaultWidth = false)
        ) {
            Surface(modifier = Modifier.fillMaxSize()) {
                PinDialog(
                    error = pinMessage,
                    onBack = {
                        pinDialog = false
                    },
                    onPin = { pin ->
                        selectedPin = pin
                        pinDialog = false
                        event(
                            QrShowEvent.Generate(
                                accountNumber = selectedAccount?.account.orEmpty(),
                                pin = selectedPin
                            )
                        )
                    },
                    deletePin = { action ->
                        deletePin = action
                    }
                )
            }
        }
    }

    // Account
    var accountBottomSheet by rememberSaveable { mutableStateOf(false) }

    if (state.accountList.isNotEmpty()) {
        BottomSheet(
            showBottomSheet = accountBottomSheet,
            onShowBottomSheet = { accountBottomSheet = it }
        ) { dismiss ->
            QrisAccountBottomSheet(
                selectedAccount = selectedAccount?.account.orEmpty(),
                nominal = "",
                data = state.accountList,
                onSelect = { item ->
                    dismiss {
                        selectedAccount = item
                        brightness = false
                        pinDialog = true
                    }
                },
                onRefresh = { item ->
                    event(QrShowEvent.RefreshSaldo(item.account.orEmpty()))
                },
                onClose = {
                    dismiss {}
                },
                fastMenu = state.qrShowRoute.fastMenu
            )
        }
    }

    // Account List
    LaunchedEffect(state.accountList) {
        if (state.accountList.isNotEmpty()) {
            selectedAccount = state.accountList.find { it.default == 1 }
                ?: state.accountList.firstOrNull()
            if (selectedAccount != null) {
                pinDialog = true
            }
        }
    }

    // Generate
    LaunchedEffect(state.qrCpmGenerate) {
        state.qrCpmGenerate
            .onSuccess { data ->
                brightness = true
                expireTime = data.expireTime ?: 0
                event(QrShowEvent.CheckStatus)
            }
            .onError { e ->
                deletePin()
                if (e is MessageException && e.errorSnackbar()) {
                    snackbarType = SnackbarType.ERROR
                    snackbarHostState.showSnackbar(e.description)
                } else {
                    error = e
                    retry = QrShowEvent.Generate(
                        accountNumber = selectedAccount?.account.orEmpty(),
                        pin = selectedPin
                    )
                    errorBottomSheet = true
                }
            }
    }

    // Check Status
    LaunchedEffect(state.qrCpmCheckStatus) {
        state.qrCpmCheckStatus
            .onSuccess { data ->
                if (data.referenceNumber == null) {
                    delay(5000)
                    if (!expired) {
                        event(QrShowEvent.CheckStatus)
                    }
                } else {
                    val qrShowData = data.serialize().orEmpty()
                    if (qrShowData.isEmpty()) {
                        retry = QrShowEvent.CheckStatus
                        errorBottomSheet = true
                    } else {
                        navigation(QrShowNavigation.Payment(qrShowData))
                    }
                }
                event(QrShowEvent.ResetCheckStatus)
            }
            .onError { e ->
                if (e is MessageException && e.errorSnackbar()) {
                    if (e.code != "01") {
                        snackbarType = SnackbarType.ERROR
                        snackbarHostState.showSnackbar(e.description)
                    }
                    delay(5000)
                    event(QrShowEvent.CheckStatus)
                } else {
                    error = e
                    retry = QrShowEvent.CheckStatus
                    errorBottomSheet = true
                }
                event(QrShowEvent.ResetCheckStatus)
            }
    }

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        bottomBar = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White)
            ) {
                DividerHorizontal()

                if (selectedAccount != null) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(start = 16.dp, top = 16.dp, end = 16.dp)
                            .clickable {
                                accountBottomSheet = true
                            },
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        AsyncImage(
                            model = selectedAccount?.imagePath.orEmpty(),
                            contentDescription = null,
                            modifier = Modifier
                                .width(58.dp)
                                .height(36.dp),
                            placeholder = painterResource(id = R.drawable.thumbnail),
                            error = painterResource(id = R.drawable.thumbnail),
                            contentScale = ContentScale.Inside
                        )

                        Spacer(modifier = Modifier.width(12.dp))

                        if (state.qrShowRoute.fastMenu) {
                            Text(
                                text = selectedAccount?.accountString.orEmpty(),
                                modifier = Modifier.weight(1f),
                                fontWeight = FontWeight.SemiBold,
                                style = MaterialTheme.typography.bodyMedium
                            )
                        } else {
                            var hide by rememberSaveable { mutableStateOf(false) }
                            val icon = if (hide) {
                                R.drawable.icon_hide_eye
                            } else {
                                R.drawable.icon_unhide_eye
                            }
                            val accountString = selectedAccount?.accountString.orEmpty()
                            val account = if (hide) {
                                "${accountString.take(4)} **** ****${accountString.takeLast(4)}"
                            } else {
                                accountString
                            }
                            val balance = if (hide) {
                                "••••••"
                            } else {
                                selectedAccount?.balanceString.orEmpty()
                            }
                            val balanceError = selectedAccount?.balance == null
                            val color =
                                if (balanceError || notEnough) Color_E84040 else Color.Black

                            Column(modifier = Modifier.weight(1f)) {
                                Row(
                                    modifier = Modifier.clickable {
                                        hide = !hide
                                    },
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Text(
                                        text = account,
                                        style = MaterialTheme.typography.labelSmall
                                    )

                                    if (!balanceError) {
                                        Spacer(modifier = Modifier.width(4.dp))

                                        Image(
                                            painter = painterResource(icon),
                                            contentDescription = null,
                                            modifier = Modifier.size(16.dp),
                                            contentScale = ContentScale.Fit
                                        )
                                    }
                                }

                                Row(verticalAlignment = Alignment.CenterVertically) {
                                    if (!balanceError) {
                                        Text(
                                            text = selectedAccount?.currency.orEmpty() + balance,
                                            color = color,
                                            fontWeight = FontWeight.SemiBold,
                                            style = MaterialTheme.typography.bodyMedium
                                        )

                                        Spacer(modifier = Modifier.width(8.dp))
                                    } else {
                                        Text(
                                            text = "Gagal memuat saldo",
                                            color = Color_E84040,
                                            style = MaterialTheme.typography.bodySmall
                                        )
                                    }
                                }
                            }
                        }

                        Spacer(modifier = Modifier.width(12.dp))

                        Icon(
                            imageVector = Icons.Default.KeyboardArrowDown,
                            contentDescription = null,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                } else {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(start = 16.dp, top = 16.dp, end = 16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Shimmer(
                            modifier = Modifier
                                .width(58.dp)
                                .height(36.dp)
                        )

                        Spacer(modifier = Modifier.width(12.dp))

                        Column(modifier = Modifier.weight(1f)) {
                            Shimmer(
                                modifier = Modifier
                                    .width(180.dp)
                                    .height(14.dp)
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            Shimmer(
                                modifier = Modifier
                                    .width(120.dp)
                                    .height(14.dp)
                            )
                        }

                        Spacer(modifier = Modifier.width(12.dp))

                        Shimmer(modifier = Modifier.size(24.dp))
                    }
                }

                PrimaryButton(
                    label = "Buat QRIS Pembayaran",
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    enabled = !notEnough
                ) {
                    pinDialog = true
                }
            }
        }
    ) { innerPadding ->
        Box(modifier = Modifier.fillMaxSize()) {
            SnackbarCustom(
                modifier = Modifier
                    .padding(innerPadding)
                    .padding(16.dp),
                hostState = snackbarHostState,
                type = snackbarType
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .paint(
                        painter = painterResource(R.drawable.form_background),
                        sizeToIntrinsics = false,
                        contentScale = ContentScale.Crop
                    )
                    .padding(innerPadding)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(R.drawable.icon_back),
                        contentDescription = null,
                        modifier = Modifier
                            .size(32.dp)
                            .clickable {
                                navigation(QrShowNavigation.Back)
                            },
                        contentScale = ContentScale.Fit
                    )

                    Text(
                        text = "QRIS Pembayaran",
                        modifier = Modifier
                            .weight(1f)
                            .padding(horizontal = 16.dp)
                            .padding(end = 32.dp),
                        color = Color.White,
                        fontWeight = FontWeight.SemiBold,
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.titleLarge
                    )
                }

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            Color.White,
                            RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)
                        )
                        .padding(horizontal = 16.dp)
                        .verticalScroll(rememberScrollState())
                ) {
                    Spacer(modifier = Modifier.height(24.dp))

                    when (state.qrCpmGenerate) {
                        is UiState.Success -> {
                            val data = state.qrCpmGenerate.data

                            Notice(
                                description = data.title.orEmpty(),
                                type = NoticeType.WARNING
                            )
                        }

                        else -> {
                            Notice(
                                description = "Sebelum buat QRIS Pembayaran, pastikan nominal di mesin kasir sudah sesuai.",
                                type = NoticeType.INFO
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    Image(
                        painter = painterResource(R.drawable.qr_black),
                        contentDescription = null,
                        modifier = Modifier
                            .size(64.dp)
                            .align(Alignment.CenterHorizontally),
                        contentScale = ContentScale.Fit
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    when (state.qrCpmGenerate) {
                        is UiState.Success -> {
                            val data = state.qrCpmGenerate.data

                            LaunchedEffect(expireTime) {
                                if (expireTime > 0) {
                                    delay(1000)
                                    expireTime--
                                }
                            }

                            if (expired) {
                                Image(
                                    painter = painterResource(R.drawable.image_qr_error),
                                    contentDescription = null,
                                    modifier = Modifier
                                        .size(240.dp)
                                        .align(Alignment.CenterHorizontally),
                                    contentScale = ContentScale.Fit
                                )
                            } else {
                                Box(
                                    modifier = Modifier
                                        .size(240.dp)
                                        .align(Alignment.CenterHorizontally),
                                    contentAlignment = Alignment.Center
                                ) {
                                    val stringQr = data.stringQr.orEmpty()

                                    if (stringQr.isNotEmpty()) {
                                        val sizePx = with(density) { 240.dp.toPx().toInt() }
                                        val bitmap = remember(stringQr) {
                                            generateQrCode(stringQr, sizePx)
                                        }

                                        Image(
                                            bitmap = bitmap.asImageBitmap(),
                                            contentDescription = null,
                                            modifier = Modifier
                                                .size(240.dp)
                                                .clip(RoundedCornerShape(16.dp))
                                                .border(
                                                    1.dp,
                                                    Color_E9EEF6,
                                                    RoundedCornerShape(16.dp)
                                                ),
                                            contentScale = ContentScale.Crop
                                        )

                                        Image(
                                            painter = painterResource(R.drawable.qr_logo),
                                            contentDescription = null,
                                            modifier = Modifier.size(32.dp),
                                            contentScale = ContentScale.Fit
                                        )
                                    }
                                }
                            }

                            Spacer(modifier = Modifier.height(24.dp))

                            Text(
                                text = "Silakan lakukan pembayaran\nmenggunakan kode QR di atas ini.",
                                modifier = Modifier.fillMaxWidth(),
                                textAlign = TextAlign.Center,
                                style = MaterialTheme.typography.bodyMedium
                            )

                            Spacer(modifier = Modifier.height(16.dp))

                            Row(
                                modifier = Modifier.align(Alignment.CenterHorizontally),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = "Masa berlaku",
                                    style = MaterialTheme.typography.bodyMedium
                                )

                                Spacer(modifier = Modifier.width(8.dp))

                                Image(
                                    painter = painterResource(R.drawable.icon_clock),
                                    contentDescription = null,
                                    modifier = Modifier.size(16.dp),
                                    contentScale = ContentScale.Fit
                                )

                                Spacer(modifier = Modifier.width(4.dp))

                                Text(
                                    text = expireText,
                                    color = Color_0054F3,
                                    style = MaterialTheme.typography.bodyLarge
                                )
                            }
                        }

                        else -> {
                            when (state.qrCpmGenerate) {
                                UiState.Loading -> {
                                    Shimmer(
                                        modifier = Modifier
                                            .size(240.dp)
                                            .align(Alignment.CenterHorizontally)
                                    )
                                }

                                else -> {
                                    Image(
                                        painter = painterResource(R.drawable.image_qr_scan),
                                        contentDescription = null,
                                        modifier = Modifier
                                            .size(240.dp)
                                            .align(Alignment.CenterHorizontally),
                                        contentScale = ContentScale.Fit
                                    )
                                }
                            }

                            Spacer(modifier = Modifier.height(24.dp))

                            Text(
                                text = "Pilih Sumber Dana dan masukkan PIN untuk menampilkan kode QR di atas.",
                                modifier = Modifier.fillMaxWidth(),
                                textAlign = TextAlign.Center,
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewQrShow() {
    MainTheme {
        QrShowContent(
            state = QrShowState(
                qrShowRoute = QrShowRoute(
                    fastMenu = false
                ),
                accountList = listOf(
                    AccountResponse(
                        account = "***************",
                        accountString = "1234 5678 9012 345",
                        name = "Name",
                        currency = "Rp",
                        cardNumber = "****************",
                        cardNumberString = "0987 XXXX XXXX 8765",
                        productType = "",
                        accountType = "",
                        scCode = "",
                        default = 0,
                        alias = "Alias",
                        minimumBalance = "",
                        limit = "",
                        limitString = "",
                        imageName = "",
                        imagePath = "",
                        onHold = false,
                        balance = "9000000",
                        balanceString = "9.000.000,00"
                    )
                )
            )
        )
    }
}

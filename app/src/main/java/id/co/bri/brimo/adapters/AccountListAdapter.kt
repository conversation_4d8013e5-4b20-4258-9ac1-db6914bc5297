package id.co.bri.brimo.adapters

import android.content.Context
import android.graphics.Typeface
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ItemSourceOfAccountBinding
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.calendar.makeGone
import id.co.bri.brimo.domain.helpers.calendar.makeVisible
import id.co.bri.brimo.models.AccountModel

class AccountListAdapter(
    private val context: Context,
    private val accounts: ArrayList<AccountModel>,
    private val onItemClick: (AccountModel) -> Unit
) : RecyclerView.Adapter<AccountListAdapter.AccountViewHolder>(){

    inner class AccountViewHolder(
        private val binding: ItemSourceOfAccountBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(account: AccountModel) {

            if (account.isDefault == 1) {
                binding.viewStatus.makeVisible()
            } else {
                binding.viewStatus.makeGone()
            }

            if (account.alias.isNotEmpty()) {
                binding.tvAccountName.text = account.alias
            } else {
                binding.tvAccountName.text = account.name
            }

            binding.tvAccountNumber.text = account.acoountString

            val image = if (account.imageCardMiniPath.isNullOrEmpty()) {
                R.drawable.ic_placeholder_ns
            } else {
                account.imageCardMiniPath
            }

            Glide.with(context)
                .load(image)
                .placeholder(R.drawable.ic_placeholder_ns)
                .error(R.drawable.ic_placeholder_ns)
                .into(binding.ivCardLogo)

            binding.root.setOnClickListener {
                onItemClick(account)
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AccountViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        val binding = ItemSourceOfAccountBinding.inflate(inflater, parent, false)
        return AccountViewHolder(binding)
    }

    override fun onBindViewHolder(holder: AccountViewHolder, position: Int) {
        holder.bind(accounts[position])
    }

    override fun getItemCount(): Int = accounts.size

    fun setData(newList: List<AccountModel>) {
        accounts.clear()
        accounts.addAll(newList)
        notifyDataSetChanged()
    }

}
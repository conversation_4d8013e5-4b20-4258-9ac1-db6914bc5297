package id.co.bri.brimo.ui.activities.inforekeningnewskin

import android.Manifest
import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.drawable.Drawable
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.annotation.RequiresApi
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import androidx.core.view.isVisible
import com.bumptech.glide.Glide
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.detailrekening.IInfoRekeningPresenter
import id.co.bri.brimo.contract.IView.detailrekening.IInfoRekeningView
import id.co.bri.brimo.databinding.ActivityInformasiRekeningBinding
import id.co.bri.brimo.databinding.ItemInfoRekeningBinding
import id.co.bri.brimo.domain.SnackBarType
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.domain.helpers.calendar.makeGone
import id.co.bri.brimo.domain.helpers.calendar.makeVisible
import id.co.bri.brimo.domain.providers.CardType
import id.co.bri.brimo.models.apimodel.request.PinFinansialRequest
import id.co.bri.brimo.models.apimodel.request.notificationsetting.GetListNotificationSettingRequest
import id.co.bri.brimo.models.apimodel.request.pengelolaankartu.DetailKelolaKartuReq
import id.co.bri.brimo.models.apimodel.response.BiFastAccountResponse
import id.co.bri.brimo.models.apimodel.response.DetailBiFast
import id.co.bri.brimo.models.apimodel.response.EnableKartuResponse
import id.co.bri.brimo.models.apimodel.response.GeneralOtpResponse
import id.co.bri.brimo.models.apimodel.response.ListRekeningResponse
import id.co.bri.brimo.models.apimodel.response.QuestionResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.notificationsetting.NotificationSettingResponse
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.ForceUpdateResponse
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.DetailKelolaKartuRes
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.activities.carddetailnewskin.PyhsicCardDetailActivity
import id.co.bri.brimo.ui.activities.carddetailnewskin.VirtualCardDetailActivity.Companion.launchIntent
import id.co.bri.brimo.ui.activities.newskinonboarding.OnboardingInputNumberForgetPassActivity
import id.co.bri.brimo.ui.fragments.PinFragmentNewSkin
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment.showDialogInformation1Button
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment.showDialogInformationWithActionSelection
import id.co.bri.brimo.util.extension.makeDisable
import id.co.bri.brimo.util.extension.makeEnable
import javax.inject.Inject

class InformasiRekeningActivity : NewSkinBaseActivity(), PinFragmentNewSkin.SendPin, IInfoRekeningView {

    @Inject
    lateinit var presenter: IInfoRekeningPresenter<IInfoRekeningView>

    private var mBiFastAccountResponse: BiFastAccountResponse? = null

    private var detailBiFastPhone: DetailBiFast? = null
    private var detailBiFastEmail: DetailBiFast? = null
    private var aliasPhone: String? = ""
    private var aliasEmail: String? = ""
    private var cardType: String? = ""
    private var cardNumber: String? = ""

    private lateinit var binding: ActivityInformasiRekeningBinding
    private var skeletonListItem: SkeletonScreen? = null
    private var finansialStatus = 0
    private var acountbiasa: String? = null
    private var nama: String? = null
    private var accountString: String? = null
    private var cardImage: String? = null
    private var accountStatus = 0
    private var newAlias: String? = null
    private var alias: String? = null
    private var noKartuMask: String? = null
    private var position: String? = null
    private var jsonResponse: String? = null

    private var errorMessage: String? = null
    private var message: String? = null
    private val infoList: MutableList<InfoRowData> = mutableListOf()

    var pinFragment: PinFragmentNewSkin? = null
    private var cardNumberView: View? = null
    private var aliasView: View? = null
    private var mBiFastView: View? = null
    private var accountStatusView: View? = null
    private var financialStatusView: View? = null
    private var notifikasiTransaksiView: View? = null
    var resultIntent: Intent = Intent()
    private var hasShownErrorDialog = false
    private var isFromNotification = false;

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityInformasiRekeningBinding.inflate(layoutInflater)
        setContentView(binding.root)

        injectDependency()
        setupToolbar()
        initSkeleton()
        setupView()

        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                handleBackPressed()
            }
        })

    }

    private fun setupToolbar()  {
        GeneralHelperNewSkin.setToolbar(
            this,
            binding.toolbar.toolbar,
            GeneralHelper.getString(R.string.text_informasi_rekening)
        )
    }

    private fun injectDependency() {
        getActivityComponent().inject(this)
        presenter.view = this
        skeletonListItem?.show()
        presenter.setView(this)
        presenter.setUrlStatus(GeneralHelper.getString(R.string.url_check_status_kartu))
        presenter.setUrlDefault(GeneralHelper.getString(R.string.url_set_default_rekening))
        presenter.setUrlBiFast(GeneralHelper.getString(R.string.url_akun_bifast))
        presenter.setUrlInfoSaldoHold(GeneralHelper.getString(R.string.url_qna_detail))
        presenter.setUrlDetailStatus(GeneralHelper.getString(R.string.url_card_management_card_detail_v2))
        presenter.start()
        rekeningData?.let {
            acountbiasa = it.account
            getNotificationSetting()
            presenter.getStatusKartu(it.account)
        }
    }

    private fun initSkeleton() {

        skeletonListItem =
            Skeleton.bind(binding.viewParent).shimmer(true).angle(20).duration(1200)
                .color(R.color.white).load(R.layout.item_skeleton_informasi_rekening).show()

    }

    private fun openPin() {
        pinFragment = PinFragmentNewSkin(this, this)
        pinFragment?.show()
    }

    private fun setupView() {
        binding.content.bringToFront()

        rekeningData?.let { data ->

            accountString = data.accountString
            acountbiasa = data.account
            cardImage = data.imageCardMiniPath
            accountStatus = data.default ?: 0
            nama = data.name
            alias = data.alias
//            noKartuMask = data.cardNumber
            finansialStatus = data.finansialStatus ?: 0

            if (data.alias.isNotEmpty()) {
                binding.tvAccountName.text = data.alias
            } else {
                binding.tvAccountName.text = nama
            }

            val image = if (cardImage.isNullOrEmpty()) {
                R.drawable.dummy_britama
            } else {
                cardImage
            }

            Glide.with(this)
                .load(image)
                .into(binding.ivCardLogo)

            // saldo on hold
            binding.viewSaldoOnhold.isVisible = data.saldoReponse?.isOnHold == true

            infoList.clear()
            infoList.addAll(
                listOf(
                    InfoRowData(getString(R.string.nomor_rekening), accountString, "copy"),
                    InfoRowData(getString(R.string.nomor_kartu), noKartuMask, "text"),
                    InfoRowData(getString(R.string.account_name), nama, "text"),
                    InfoRowData(getString(R.string.alias_name), alias, "arrow"),
                    InfoRowData(
                        getString(R.string.financial_state),
                        if (finansialStatus == 0) GeneralHelper.getString(R.string.tidak_aktif) else GeneralHelper.getString(R.string.aktif),
                        "arrow"
                    ),
                    InfoRowData(
                        getString(R.string.account_status),
                        if (accountStatus == 0) GeneralHelper.getString(R.string.not_primary_account) else GeneralHelper.getString(R.string.primary_account),
                        "arrow"
                    ),
                    InfoRowData(getString(R.string.status_notication_transaction), "Tidak Aktif", "arrow")
                )
            )

            binding.containerLayout.removeAllViews()
            infoList.forEach { binding.containerLayout.addView(createInfoRow(this, it)) }

            disableMainAccount()

            // Alias
            if (alias.isNullOrEmpty()) {
                updateAlias(GeneralHelper.getString(R.string.hint_no_alias))
            } else {
                updateAlias(alias)
            }

            // Enable/disable main account
            if (data.currency.equals(Constant.CURRENCY, ignoreCase = true)) enableMainAccount() else disableMainAccount()

            // Disable financial status if on hold
            if (data.saldoReponse?.isOnHold == true) disableFinancialStatus()

            // Connect card
            binding.viewConnectCard.setOnClickListener {
                presenter.getDataDetailStatus(
                    DetailKelolaKartuReq(data.cardNumber, data.account)
                )
            }
        }

    }

    private fun createInfoRow(context: Context, data: InfoRowData): View {
        val binding = ItemInfoRekeningBinding.inflate(LayoutInflater.from(context))

        binding.tvLabelInfoRekening.text = data.label
        binding.tvNamaInfoRekening.text = data.value

        when (data.type) {
            Constant.COPY -> {
                binding.imgCopy.visibility = View.VISIBLE
                binding.imgCopy.setOnClickListener {
                    GeneralHelper.copyToClipboard(context, Constant.COPY, acountbiasa)
                    GeneralHelperNewSkin.showCustomSnackBar(this.binding.content, ContextCompat.getString(context, R.string.info_saldo_rekening_berhasil_disalin), SnackBarType.SUCCESS)
                }
            }
            Constant.ARROW -> binding.imgArrow.visibility = View.VISIBLE
            else -> {
                binding.imgCopy.visibility = View.GONE
                binding.imgArrow.visibility = View.GONE
            }
        }

        // Store reference to specific views
        when (data.label) {
            context.getString(R.string.nomor_kartu) -> cardNumberView = binding.root
            context.getString(R.string.alias_name) -> aliasView = binding.root
            context.getString(R.string.proxy_bi_fast) -> mBiFastView = binding.root
            context.getString(R.string.account_status) -> accountStatusView = binding.root
            context.getString(R.string.financial_state) -> financialStatusView = binding.root
            context.getString(R.string.status_notication_transaction) -> notifikasiTransaksiView = binding.root
        }

        binding.root.setOnClickListener {
            when (data.label) {
                context.getString(R.string.alias_name) -> {
                    var newAlias = alias
                    if (alias?.contains("@") != true && alias != "" ) {
                        newAlias = "@$alias"
                    }
                    EditAliasNameActivity.launchIntent(this, newAlias, acountbiasa)
                }
                context.getString(R.string.proxy_bi_fast) -> { // proxy BI-Fast
//                    if (mBiFastAccountResponse != null) {
//                        ProxyBIFastActivity.launchIntent(this, aliasPhone, aliasEmail, detailBiFastPhone, detailBiFastEmail, accountString, nama, acountbiasa)
//                    } else {
//                        showError()
//                    }
                }
                context.getString(R.string.financial_state) -> { // status finansial
                    val title = getString(if (finansialStatus == 0) R.string.txt_activate_finansial else R.string.nonaktifkan_finansial)
                    val message = getString(if (finansialStatus == 0) R.string.txt_activate_finansial_desc else R.string.txt_nonaktifkan_finansial_desc)
                    confirmAction(title, message, Constant.FINANCIAL_STATE)
                }
                context.getString(R.string.account_status) -> { // status rekening
                    confirmAction(
                        context.getString(R.string.make_main_account),
                        context.getString(R.string.txt_make_main_account_desc),
                        Constant.MAIN_ACCOUNT
                    )
                }
                context.getString(R.string.status_notication_transaction) -> { // status notifikasi transaksi
                    jsonResponse?.let { it1 ->
                        TransactionNotificationActivity.launchIntent(this,
                            it1, accountString, alias, cardImage, nama)
                    }
                }
            }
        }

        return binding.root
    }

    private fun updateCardNumber(cardNumber: String?) {
        cardNumberView?.let { view ->
            val cardNumberBinding = ItemInfoRekeningBinding.bind(view)
            cardNumberBinding.tvNamaInfoRekening.text = cardNumber
        }
    }

    private fun updateAlias(newAlias: String?) {
        aliasView?.let { view ->
            val aliasBinding = ItemInfoRekeningBinding.bind(view)
            aliasBinding.tvNamaInfoRekening.text = newAlias
            rekeningData?.alias = newAlias
        }
    }

    private fun updateBiFast(biFast: String?) {
        mBiFastView?.let { view ->
            val mBIFastBinding = ItemInfoRekeningBinding.bind(view)
            mBIFastBinding.tvNamaInfoRekening.text = biFast
        }
    }

    private fun showBiFast(isShow: Boolean) {
        mBiFastView?.let { view ->
            val mBIFastBinding = ItemInfoRekeningBinding.bind(view)
            if (isShow) {
                mBIFastBinding.root.makeVisible()
            } else {
                mBIFastBinding.root.makeGone()
            }
        }
    }

    private fun updateFinancialStatus(status: String?) {
        financialStatusView?.let { view ->
            val binding = ItemInfoRekeningBinding.bind(view)
            binding.tvNamaInfoRekening.text = status
        }
    }

    private fun updateAccountStatus(accountStatus: String?) {
        accountStatusView?.let { view ->
            val accountStatusBinding = ItemInfoRekeningBinding.bind(view)
            accountStatusBinding.tvNamaInfoRekening.text = accountStatus
        }
    }

    private fun updateNotification(status: String?) {
        notifikasiTransaksiView?.let { view ->
            val binding = ItemInfoRekeningBinding.bind(view)
            binding.tvNamaInfoRekening.text = status
        }
    }

    private fun disableMainAccount() {
        accountStatusView?.let { view ->
            val mainAccountBinding = ItemInfoRekeningBinding.bind(view)
            mainAccountBinding.root.makeDisable()
//            mainAccountBinding.tvLabelInfoRekening.setTextColor(ContextCompat.getColor(this, R.color.disable_default_ns))
//            mainAccountBinding.tvNamaInfoRekening.setTextColor(ContextCompat.getColor(this, R.color.disable_default_ns))
            mainAccountBinding.imgArrow.makeGone()
        }
    }

    private fun enableMainAccount() {
        accountStatusView?.let { view ->
            val mainAccountBinding = ItemInfoRekeningBinding.bind(view)
            mainAccountBinding.root.makeEnable()
            mainAccountBinding.tvLabelInfoRekening.setTextColor(ContextCompat.getColor(this, R.color.ns_black500))
            mainAccountBinding.tvNamaInfoRekening.setTextColor(ContextCompat.getColor(this, R.color.black_ns_main))
            mainAccountBinding.imgArrow.makeVisible()
        }
    }

    private fun disableFinancialStatus() {
        financialStatusView?.let { view ->
            val mainAccountBinding = ItemInfoRekeningBinding.bind(view)
            mainAccountBinding.root.makeDisable()
            mainAccountBinding.tvLabelInfoRekening.setTextColor(ContextCompat.getColor(this, R.color.disable_default_ns))
            mainAccountBinding.tvNamaInfoRekening.setTextColor(ContextCompat.getColor(this, R.color.disable_default_ns))
            mainAccountBinding.imgArrow.makeGone()
        }
    }

    private fun setupViewBiFast() {
        val response = mBiFastAccountResponse

        if (response == null) {
            updateBiFast(GeneralHelper.getString(R.string.set_now))
            return
        }

        detailBiFastEmail = response.emailAlias
        detailBiFastPhone = response.phoneAlias
        aliasEmail = response.emailAddress
        aliasPhone = response.phoneNumber

        val matchedAlias = when {
            !detailBiFastPhone?.account.isNullOrEmpty() && acountbiasa == detailBiFastPhone?.account -> detailBiFastPhone
            !detailBiFastEmail?.account.isNullOrEmpty() && acountbiasa == detailBiFastEmail?.account -> detailBiFastEmail
            else -> null
        }

        val value = when (matchedAlias?.status) {
            Constant.STATUS_1 -> matchedAlias.proxyValue
            Constant.STATUS_2 -> GeneralHelper.getString(R.string.tidak_aktif)
            Constant.STATUS_3 -> GeneralHelper.getString(R.string.set_now)
            else -> GeneralHelper.getString(R.string.set_now)
        }

        // for next development
//        if (matchedAlias?.proxyType.equals(Constant.PROXY_PHONE) && matchedAlias?.status.equals(Constant.STATUS_1)) {
//            updateBiFast(GeneralHelperNewSkin.formatPhoneNumber(value))
//        } else {
//            updateBiFast(value)
//        }
        
        if ((matchedAlias?.proxyType == Constant.PROXY_PHONE ||
                    matchedAlias?.proxyType == Constant.PROXY_EMAIL) &&
            matchedAlias.status == Constant.STATUS_1) {

            val indexAlias = infoList.indexOfFirst { it.label == getString(R.string.alias_name) }
            if (indexAlias != -1) {
                infoList.add(
                    indexAlias + 1,
                    InfoRowData(getString(R.string.proxy_bi_fast), getString(R.string.aktif), "text")
                )
                binding.containerLayout.removeAllViews()
                infoList.forEach { binding.containerLayout.addView(createInfoRow(this, it)) }
            }

        } else {
            showBiFast(false)
        }

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (data != null) {
            if (requestCode == Constant.REQ_EDIT_SAVED) {
                if (resultCode == RESULT_OK) {
                    newAlias = data.getStringExtra(Constant.TAG_ALIAS)
                    position = data.getStringExtra(Constant.TAG_POSITION)
                    message = GeneralHelper.getString(R.string.nickname_changed_successfully_ns)
                    message?.let { GeneralHelperNewSkin.showSnackBarGreen(binding.content, it) }
                    this.setResult(RESULT_OK, data)
                    alias = newAlias
                    updateAlias("@$alias")
                        resultIntent.putExtra(Constant.TAG_ALIAS, alias)
                    resultIntent.putExtra(Constant.TAG_POSITION, position)
                }
            } else if (requestCode == Constant.REQ_ALIAS) {
                if (resultCode == RESULT_OK) {
                    message = data.getStringExtra(Constant.TAG_MESSAGE)
//                    message?.let { GeneralHelperNewSkin.showSnackBarGreen(binding.content, it) }
                    val status = data.getStringExtra(Constant.TAG_TYPE)
                    val alias = data.getStringExtra(Constant.TAG_ALIAS_BIFAST)
                    val account = data.getStringExtra(Constant.TAG_ALIAS_AKUN)
                    val value = data.getStringExtra(Constant.TAG_VALUE)

                    when (status) {
                        Constant.STATUS_1 -> {
                            if (alias == RestResponse.ResponseCodeEnum.RC_01.value) {
                                updateBiFast(value?.let { GeneralHelperNewSkin.formatPhoneNumber(it) })
                            } else {
                                updateBiFast(value)
                            }
                        }
                        Constant.STATUS_0 -> updateBiFast(GeneralHelper.getString(R.string.tidak_aktif))
                        Constant.STATUS_MIN1 -> updateBiFast(GeneralHelper.getString(R.string.set_now))
                    }

                    when (alias) {
                        RestResponse.ResponseCodeEnum.RC_01.value -> {
                            detailBiFastPhone?.status = status
                            detailBiFastPhone?.account = account
                        }
                        RestResponse.ResponseCodeEnum.RC_02.value -> {
                            detailBiFastEmail?.status = status
                            detailBiFastEmail?.account = account
                        }
                    }
                }
            } else if (requestCode == Constant.REQ_FINANSIAL) {
                if (resultCode == RESULT_OK) {
                    if (finansialStatus == 0) {
                        finansialStatus = 1
                    } else {
                        finansialStatus = 0
                    }
                }
            } else if (requestCode == Constant.RESULT_BACK) {
                isFromNotification = true
                skeletonListItem?.show()
                getNotificationSetting()
            }
        }
    }

    private fun confirmAction(title: String, subtitle: String, type: String) {

        val firstBtnFunction = Runnable {
            when (type) {
                Constant.FINANCIAL_STATE -> { // status finansial
                    presenter.setUrlFinansialRek(GeneralHelper.getString(R.string.url_financial_deactivate_account))
                    openPin()
                }
                Constant.MAIN_ACCOUNT -> { // status rekening
                    if (finansialStatus == 0 ) {
                        val firstBtnFunction = Runnable {

                        }

                        showDialogInformation1Button(
                            supportFragmentManager,
                            "",
                            Constant.IC_SAD_NEW_NS,
                            getString(R.string.txt_financial_status_confirm_title),
                            getString(R.string.txt_financial_status_confirm_desc),
                            createKotlinFunction0(firstBtnFunction),
                            false,
                            ContextCompat.getString(this, R.string.onboarding_form_bs_understand_button),
                            false,
                            showPill = false
                        )

                    } else {
                        presenter.setChangeDefault(acountbiasa)
                    }
                }
            }
        }

        val secBtnFunction = Runnable {

        }
        val buttonFirstText = when (type) {
            Constant.FINANCIAL_STATE -> {
                if (finansialStatus == 0) {
                    ContextCompat.getString(this, R.string.aktifkan_2)
                } else ContextCompat.getString(this, R.string.nonaktifkan)
            }
            Constant.MAIN_ACCOUNT -> ContextCompat.getString(this, R.string.txt_main_account_btn)
            else -> ""
        }

        if (type == Constant.FINANCIAL_STATE && finansialStatus == 0) {

            val firstBtnFunction2: Runnable = Runnable {

            }

            showDialogInformation1Button(
                supportFragmentManager,
                "",
                Constant.IC_WARNING_NEWSKIN_NS,
                title,
                subtitle,
                createKotlinFunction0(firstBtnFunction2),
                true,
                ContextCompat.getString(this, R.string.onboarding_form_bs_understand_button),
                true,
                showPill = true
            )

        } else {
            showDialogConfirmation(
                supportFragmentManager,
                R.drawable.ic_warning_new_ns,
                "",
                title,
                subtitle,
                createKotlinFunction0(firstBtnFunction),
                createKotlinFunction0(secBtnFunction),
                true,
                buttonFirstText,
                ContextCompat.getString(this, R.string.btn_cancel),
                false,
                showCloseButton = true,
                showPill = true
            )
        }


    }

    private fun getNotificationSetting() {
        presenter.setUrlGetListNotification(GeneralHelper.getString(R.string.url_v1_check_status_notif))
        acountbiasa?.let { presenter.getListNotificationSetting(GetListNotificationSettingRequest(it)) }
    }

    private fun showError() {

        skeletonListItem?.hide()

        if (hasShownErrorDialog) return

        hasShownErrorDialog = true

        val firstBtnFunction = Runnable {
            skeletonListItem?.show()
            hasShownErrorDialog = false
            getNotificationSetting()
            rekeningData?.let { presenter.getStatusKartu(it.account) }
            presenter.getListBiFast()
        }
        OpenBottomSheetGeneralNewSkinFragment.showDialogInformationDismiss(
            supportFragmentManager,
            "",
            Constant.IC_SAD_NEW_NS,
            getString(R.string.failed_load_page_title),
            getString(R.string.failed_load_page_subtitle),
            createKotlinFunction0(firstBtnFunction),
            true,
            getString( R.string.refresh),
            true,
            showPill = true,
        )

    }

    private fun connectCardState(status: Boolean) {
        if (!status) {
            binding.viewConnectCard.isEnabled = false
            binding.tvAccountName.setTextColor(ContextCompat.getColor(this, R.color.disable_default_ns))
            binding.tvAccountNumber.setTextColor(ContextCompat.getColor(this, R.color.disable_default_ns))
            binding.ivArrow.makeGone()
            binding.ivArrow.alpha = 0.3f
        } else {
            binding.viewConnectCard.isEnabled = true
            binding.tvAccountName.setTextColor(ContextCompat.getColor(this, R.color.ns_black900))
            binding.tvAccountNumber.setTextColor(ContextCompat.getColor(this, R.color.black_ns_main))
            binding.ivArrow.makeVisible()
            binding.ivArrow.alpha = 1.0f
        }
    }

    private fun validateMainAccount() {

        if (accountStatus == 0) {
            updateAccountStatus(GeneralHelper.getString(R.string.not_primary_account))
            val currency = rekeningData?.currency
            if (currency?.equals(Constant.CURRENCY, ignoreCase = true) == true) {
                enableMainAccount()
            } else {
                disableMainAccount()
            }
        } else {
            updateAccountStatus(GeneralHelper.getString(R.string.primary_account))
            disableMainAccount()
        }

//        if (rekeningData?.currency.equals(Constant.CURRENCY, ignoreCase = true))
//            enableMainAccount()
//        else
//            disableMainAccount()
    }

    override fun onResp00(enableKartuResponse: EnableKartuResponse) {

        cardType = enableKartuResponse.type
        cardNumber = enableKartuResponse.cardNumber
        skeletonListItem?.hide()

        noKartuMask = GeneralHelperNewSkin.formatPhoneNumber(enableKartuResponse.cardNumber)

        if (noKartuMask.equals("")) {
            updateCardNumber(getString(R.string.hint_no_card))
            binding.tvAccountNumber.text = getString(R.string.hint_no_card)
        } else {
            updateCardNumber(noKartuMask)
            binding.tvAccountNumber.text = noKartuMask
        }

        if (enableKartuResponse.cardActive) {
            binding.ivCardLogo.alpha = 1.0f
        } else {
            binding.ivCardLogo.alpha = 0.3f
        }

        connectCardState(noKartuMask != "")

        validateMainAccount()

    }

    override fun onException01(restResponse: RestResponse?) {
        connectCardState(false)
        binding.ivCardLogo.alpha = 0.3f
        updateCardNumber(GeneralHelper.getString(R.string.hint_no_card))
        validateMainAccount()
        showError()
    }

    override fun onException12(message: String?, type: String) {
        when (type) {
            Constant.REQUEST_REKENING_UTAMA -> {
                GeneralHelperNewSkin.showCustomSnackBar(binding.content, getString(R.string.txt_failed_main_account), SnackBarType.ERROR)
            }
            Constant.REQUEST_STATUS_FINANSIAL -> {
                GeneralHelperNewSkin.showCustomSnackBar(binding.content, getString(R.string.txt_failed_rekening_finansial), SnackBarType.ERROR)
            }
            else -> {
                showError()
            }
        }
        // error pin
        pinFragment?.onWrongPin(message, this)
    }

    override fun onSuccessGetChangeDefault(message: String?) {
        resultIntent.putExtra(Constant.TAG_POSITION_DEFAULT, acountbiasa)
        updateAccountStatus(GeneralHelper.getString(R.string.primary_account))
        GeneralHelperNewSkin.showCustomSnackBar(binding.content, getString(R.string.txt_success_main_account), SnackBarType.SUCCESS)
        disableMainAccount()
    }
    override fun onSuccessBiFast(biFastAccountResponse: BiFastAccountResponse?) {
        mBiFastAccountResponse = biFastAccountResponse
//        skeletonListItem?.hide()
        setupViewBiFast()
    }

    override fun onGetSuccessDetail(detailKelolaKartuRes: DetailKelolaKartuRes) {
        if (cardType.equals(Constant.VIRTUAL)) {
            launchIntent(this, detailKelolaKartuRes.cardNumber, false, CardType.VIRTUAL_DEBIT)
        } else {
            PyhsicCardDetailActivity.launchIntentHomeCard(this, detailKelolaKartuRes, acountbiasa)
        }
    }

    override fun onException(message: String) {
        showError()
    }

    override fun onErrorBiFast(error: String?) {
        errorMessage = error
        showError()
    }

    override fun onSuccessGetFinansial(generalOtpResponse: GeneralOtpResponse?) {
        pinFragment?.dismiss()
        // open OTP
        hideProgress()
        if (finansialStatus == 0) {
            var selectedOtpType = ""
            val itemAction: (String) -> Unit = {
                selectedOtpType = it
            }
            val actionList = listOf(
                Triple(R.drawable.ic_sms_orange_white, "SMS", itemAction ),
                Triple(R.drawable.ic_whatsapp, "Whatsapp", itemAction)
            )
            showDialogInformationWithActionSelection(
                fragmentManager =  supportFragmentManager,
                titleTxt = ContextCompat.getString(this, R.string.pilih_metode_otp),
                subTitleTxt = getString(R.string.pilih_metode_otp_desc, generalOtpResponse?.phone.orEmpty()),
                actionList = actionList,
                btnFirstFunction = {
//                    OnboardingOtpNewSkinActivity.launchIntent(this, selectedOtpType, generalOtpResponse,Constant.REQ_FINANSIAL)
                },
                firstBtnTxt = "Kirim Kode OTP"
            )
        }

    }

    override fun onSuccessNonaktifFinansial() {
        pinFragment?.dismiss()
        GeneralHelperNewSkin.showCustomSnackBar(binding.content, getString(R.string.finansial_status_nonaktif_ns), SnackBarType.SUCCESS)
        finansialStatus = 0
        updateFinancialStatus(GeneralHelper.getString(R.string.tidak_aktif))
    }

    override fun onSuccessInfoSaldoHold(questionResponse: QuestionResponse?) {
    }

    override fun onUpdateVersion(forceUpdate: ForceUpdateResponse) {
        val clickAction = Runnable {
            openPlayStore
        }
        OpenBottomSheetGeneralNewSkinFragment.showDialogInformation(
            supportFragmentManager,
            "",
            Constant.IC_WARNING_NS,
            forceUpdate.title,
            forceUpdate.description,
            createKotlinFunction0(clickAction),
            false,
            forceUpdate.button
        )
    }

    private val openPlayStore = Runnable {
        val uri = "${Constant.MARKET_DETAIL}${Constant.PACKAGE_NAME}".toUri()
        val fallbackUri = "${Constant.PLAY_STORE}${Constant.PACKAGE_NAME}".toUri()
        val intent = Intent(Intent.ACTION_VIEW, uri)
        try {
            startActivity(intent)
        } catch (_: ActivityNotFoundException) {
            startActivity(Intent(Intent.ACTION_VIEW, fallbackUri))
        }
    }

    override fun onSuccessGetListNotification(response: NotificationSettingResponse?) {

        if (isFromNotification) {
            skeletonListItem?.hide()
            isFromNotification = false
        }

        jsonResponse = Gson().toJson(response)
        val hasActiveNotification = response?.notificationOptions?.any { it.status } == true

        if (hasActiveNotification) {
            updateNotification(GeneralHelper.getString(R.string.aktif))
        } else {
            updateNotification(GeneralHelper.getString(R.string.tidak_aktif))
        }

    }

    override fun onSendPinComplete(pin: String?) {
        presenter.sendFinansialRek(PinFinansialRequest(pin, acountbiasa), finansialStatus)
    }

    override fun onLupaPin() {
        OnboardingInputNumberForgetPassActivity.launchIntent(this, Constant.REQ_UBAH_PIN)
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        var grantAll = true
        if (grantResults.isNotEmpty()) {
            for (result in grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    grantAll = false
                    break
                }
            }
        }

        if (!grantAll) {
            showAlertFinish(getString(R.string.notes_need_permission_resi))
        } else {
            val title = getString(if (finansialStatus == 0) R.string.aktifkan_finansial else R.string.nonaktifkan_finansial)
            val message = getString(if (finansialStatus == 0) R.string.txt_aktifkan_financial_desc else R.string.txt_nonaktifkan_finansial_desc)
            confirmAction(title, message, Constant.FINANCIAL_STATE)
        }

    }

    override fun onDestroy() {
        presenter.stop()
        super.onDestroy()
    }

    private fun handleBackPressed() {
        setResult(Constant.REQ_BACK_INFORMASI, resultIntent)
        finish()
    }

    companion object {

        private var rekeningData: ListRekeningResponse.Account? = null

        private val PERMISSIONS_REGIS = arrayOf(
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.CAMERA,
            Manifest.permission.RECORD_AUDIO
        )

        @RequiresApi(Build.VERSION_CODES.TIRAMISU)
        private val PERMISSIONS_REGIS_33 = arrayOf(
            Manifest.permission.READ_MEDIA_IMAGES,
            Manifest.permission.READ_MEDIA_AUDIO,
            Manifest.permission.READ_MEDIA_VIDEO,
            Manifest.permission.CAMERA,
            Manifest.permission.RECORD_AUDIO
        )

        fun permissions(): Array<String> {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                PERMISSIONS_REGIS_33
            } else {
                PERMISSIONS_REGIS
            }
        }

        const val PERMISSIONS_ALL = 1

        @JvmStatic
        fun launchIntent(
            caller: Activity,
            rekening: ListRekeningResponse.Account
        ) {
            val intent = Intent(caller, InformasiRekeningActivity::class.java)
            rekeningData = rekening
            caller.startActivityForResult(intent, Constant.REQ_BACK_INFORMASI)
        }
    }

    override fun onSessionEnd(message: String?) {
        if (message?.contains("Salah PIN 3 kali. User Anda terblokir.") == true) {
            GeneralHelperNewSkin.showErrorBlokir(this)
        } else {
            super.onSessionEnd(message)
        }
    }

    override fun onResume() {
        super.onResume()
        hasShownErrorDialog = false
    }

}

data class InfoRowData(
    val label: String,
    val value: String?,
    val type: String,
    val status: Int = -1
)

package id.co.bri.brimo.ui.activities.bukarekeningreskin

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.app.Activity
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.DetailTransaksiRevampAdapter
import id.co.bri.brimo.adapters.SumberTransaksiRevAdapter
import id.co.bri.brimo.adapters.TotalTransaksiRevAdapter
import id.co.bri.brimo.databinding.ActivityReceiptTabunganNewskinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.ui.activities.DashboardIBActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.fragments.bukaRekening.BottomFragmentReceipt
import id.co.bri.brimo.ui.fragments.bukaRekening.BottomFragmentReceipt.Companion.mtrxType

class ReceiptTabunganNewSkinActivity : BaseActivity(), View.OnClickListener {

    lateinit var binding: ActivityReceiptTabunganNewskinBinding
    var bottomFragmentReceipt: BottomFragmentReceipt? = null

    private var detailTransaksiRevampAdapter: DetailTransaksiRevampAdapter? = null
    private var sumberTransaksiRevAdapter: SumberTransaksiRevAdapter? = null
    private var totalTransaksiRevAdapter: TotalTransaksiRevAdapter? = null

    private var isHide: Boolean = false
    private lateinit var myClipboard: ClipboardManager
    private lateinit var myClip: ClipData

    companion object {
        var mPendingResponse: ReceiptRevampResponse? = null
        private var mfromCatatanAktivitas: Boolean = false
        private var mButtonText: String = ""
        private var mTrxType: String = ""

        fun launchIntent(
            caller: Activity,
            paymentResponse: ReceiptRevampResponse,
            fromCatatanAktivitas: Boolean,
            buttonText: String,
            trxType: String = ""
        ) {
            val intent = Intent(caller, ReceiptTabunganNewSkinActivity::class.java)
            mPendingResponse = paymentResponse
            mfromCatatanAktivitas = fromCatatanAktivitas
            mButtonText = buttonText
            mTrxType = trxType
            caller.startActivityForResult(intent, Constant.REQ_BUKA_REKENING)
            caller.setResult(RESULT_OK, setResultReceipt(paymentResponse))
        }


        /**
         * Get return Intent untuk Dashboard activity
         *
         * @return
         */
        fun setResultReceipt(response: ReceiptRevampResponse): Intent? {
            val intentReturn = Intent()
            return try {
                if (response.titleImage != null) {
                    if (response.titleImage.contains("00")) {
                        intentReturn.putExtra(Constant.REQUEST_RECEIPT, true)
                        intentReturn
                    } else {
                        intentReturn.putExtra(Constant.REQUEST_RECEIPT, false)
                        intentReturn
                    }
                } else {
                    intentReturn.putExtra(Constant.REQUEST_RECEIPT, true)
                    intentReturn
                }
            } catch (e: Exception) {
                intentReturn.putExtra(Constant.REQUEST_RECEIPT, true)
                intentReturn
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityReceiptTabunganNewskinBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupView()
    }

    private fun setupView() {
        with(binding) {
            setStatusColorAndStatusBar(R.color.highlightColor, View.SYSTEM_UI_FLAG_VISIBLE);
            tvDetailTabungan.setOnClickListener(this@ReceiptTabunganNewSkinActivity)
            btnRekeningBaru.setOnClickListener(this@ReceiptTabunganNewSkinActivity)

            tvTitleTime.text = mPendingResponse!!.transactionDate
            btnRekeningBaru.text = mButtonText
            if (mTrxType == Constant.PAYMENT_SUBMIT_REGIST_DPLK) {
                tvDetailTabungan.text = GeneralHelper.getString(R.string.txt_expand_title_open_dplk)
            }

            GeneralHelper.loadIconTransaction(
                this@ReceiptTabunganNewSkinActivity,
                mPendingResponse!!.transactionImage,
                "",
                ivProduct,
                0
            )

            if (mPendingResponse?.cardAccountDataInfo != null) {
                tvCardNumber.text = mPendingResponse?.cardAccountDataInfo?.cardAccountNumberFormat
                tvCardName.text = mPendingResponse?.cardAccountDataInfo?.cardAccountName
            } else {
                tvCardName.text = mPendingResponse?.headerDataView?.get(1)?.value
                tvCardNumber.text = mPendingResponse?.headerDataView?.get(2)?.value?.let {
                    maskCardNumber(
                        it
                    )
                }
            }


            ivCopy.setOnClickListener {
                myClipboard = getSystemService(CLIPBOARD_SERVICE) as ClipboardManager
                var text: String?
                if (mPendingResponse?.cardAccountDataInfo != null){
                    text = mPendingResponse?.cardAccountDataInfo?.cardAccountNumberFormat
                } else {
                    text = mPendingResponse?.headerDataView?.get(2)?.value
                }
                myClip = ClipData.newPlainText("text", text)
                myClipboard.setPrimaryClip(myClip)
                showSnackbarErrorMessage(
                    GeneralHelper.getString(R.string.info_saldo_rekening_berhasil_disalin),
                    ALERT_CONFIRM,
                    this@ReceiptTabunganNewSkinActivity,
                    false
                )
            }

            initDetail()
        }
    }

    fun maskCardNumber(cardNumber: String): String {
        val cleaned = cardNumber.replace(" ", "")
        if (cleaned.length < 7) return cleaned

        val firstFour = cleaned.take(4)
        val lastThree = cleaned.takeLast(3)
        return "$firstFour •••• •••• $lastThree"
    }

    private fun initDetail() {
        binding.llSeeDetail.setOnClickListener {
            if (isHide) {
                isHiding(false)
            } else {
                isHiding(true)
            }
        }

        binding.rvSumberDana.apply {
            setHasFixedSize(true)
            layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
            detailTransaksiRevampAdapter = DetailTransaksiRevampAdapter(mPendingResponse?.headerDataView, context)
            adapter = detailTransaksiRevampAdapter
        }

        binding.rvNomorRekening.apply {
            setHasFixedSize(true)
            layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
            detailTransaksiRevampAdapter = DetailTransaksiRevampAdapter(mPendingResponse?.transactionDataViewPending, context)
            adapter = detailTransaksiRevampAdapter
        }

        binding.rvTanggalTransaksi.apply {
            setHasFixedSize(true)
            layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
            detailTransaksiRevampAdapter = DetailTransaksiRevampAdapter(mPendingResponse?.referenceDataView, context)
            adapter = detailTransaksiRevampAdapter
        }

        binding.rvSetoranAwal.apply {
            setHasFixedSize(true)
            layoutManager = LinearLayoutManager(context, RecyclerView.VERTICAL, false)
            sumberTransaksiRevAdapter = SumberTransaksiRevAdapter(mPendingResponse?.amountDataView, context)
            adapter = sumberTransaksiRevAdapter
        }

        if (mtrxType == Constant.PAYMENT_SUBMIT_REGIST_DPLK) {
            binding.tvDetailTabungan.text = GeneralHelper.getString(R.string.txt_expand_title_open_dplk)
        }

        if (mPendingResponse?.amountDataView == null) {
            binding.viewAmountDataView.visibility = View.GONE
        }

        if (mPendingResponse?.totalDataView != null) {
            binding.rvTotal.setHasFixedSize(true)
            binding.rvTotal.layoutManager = LinearLayoutManager(
                this, RecyclerView.VERTICAL, false
            )
            totalTransaksiRevAdapter = TotalTransaksiRevAdapter(mPendingResponse?.totalDataView, this)
            binding.rvTotal.adapter = totalTransaksiRevAdapter
        } else {
            binding.lyAbu.setBackgroundColor(GeneralHelper.getColor(R.color.whiteColor))
        }
    }

    private fun isHiding(isHiding: Boolean) {
        if (isHiding) {
            // Make sure the view is visible and layout params allow measuring
            binding.llDetail.visibility = View.VISIBLE
            binding.llDetail.layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT

            // Use post to ensure the view is fully laid out before measuring
            binding.llDetail.post {
                val targetHeight = binding.llDetail.measuredHeight

                // Collapse to height = 0 before animating
                binding.llDetail.layoutParams.height = 0
                binding.llDetail.requestLayout()

                // Animate from 0 to measured height
                val animator = ValueAnimator.ofInt(0, targetHeight)
                animator.addUpdateListener { animation ->
                    binding.llDetail.layoutParams.height = animation.animatedValue as Int
                    binding.llDetail.requestLayout()
                }
                animator.duration = 300
                animator.start()

                // Update UI elements
                binding.tvDetail.text = "Sembunyikan"
                binding.ivDetailArrow.setImageResource(R.drawable.ic_chevron_top_ns)
                isHide = true
            }

        } else {
            val initialHeight = binding.llDetail.measuredHeight

            // Animate from current height to 0
            val animator = ValueAnimator.ofInt(initialHeight, 0)
            animator.addUpdateListener { animation ->
                binding.llDetail.layoutParams.height = animation.animatedValue as Int
                binding.llDetail.requestLayout()
            }
            animator.duration = 300
            animator.addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    binding.llDetail.visibility = View.GONE
                }
            })
            animator.start()

            // Update UI elements
            binding.tvDetail.text = "Lihat Detail"
            binding.ivDetailArrow.setImageResource(R.drawable.ic_chevron_down_ns)
            isHide = false
        }
    }


    override fun onClick(p0: View?) {
        when (p0!!.id) {
            R.id.btn_rekening_baru -> {
                var intentLogin = Intent(
                    this@ReceiptTabunganNewSkinActivity,
                    DashboardIBActivity::class.java
                )
                startActivity(intentLogin)
                finish()
            }
            R.id.tvDetailTabungan -> {
                bottomFragmentReceipt = BottomFragmentReceipt.newInstance(mPendingResponse!!, mTrxType )
                bottomFragmentReceipt!!.show(supportFragmentManager, "")
            }
            else -> {}
        }
    }

    override fun onBackPressed() {
        if (mfromCatatanAktivitas) {
            super.onBackPressed()
        } else {
            val i = Intent()
            setResult(RESULT_FIRST_USER, i)
            finish()
        }
    }
}
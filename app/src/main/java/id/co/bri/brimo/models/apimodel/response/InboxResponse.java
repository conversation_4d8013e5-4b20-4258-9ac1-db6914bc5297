package id.co.bri.brimo.models.apimodel.response;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.List;

public class InboxResponse {

    @SerializedName("activity_list")
    @Expose
    private List<ActivityList> activityLists = null;
    @SerializedName("reference_number")
    @Expose
    private String referenceNumber;

    public InboxResponse(List<ActivityList> activityLists, String referenceNumber) {
        this.activityLists = activityLists;
        this.referenceNumber = referenceNumber;
    }

    public List<ActivityList> getActivityLists() {
        return activityLists;
    }

    public void setActivityLists(List<ActivityList> activityLists) {
        this.activityLists = activityLists;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public static class ActivityList {
        @SerializedName("id")
        @Expose
        private String id;
        @SerializedName("icon_name")
        @Expose
        private String iconName;
        @SerializedName("icon_path")
        @Expose
        private String iconPath;
        @SerializedName("title")
        @Expose
        private String title;
        @SerializedName("subtitle")
        @Expose
        private String subtitle;
        @SerializedName("date")
        @Expose
        private String date;
        @SerializedName("status")
        @Expose
        private String status;
        @SerializedName("trx_type")
        private String trxType;
        @SerializedName("reference_number")
        @Expose
        private String referenceNumber;
        @Expose
        @SerializedName("trx_status")
        private String trxStatus;
        @Expose
        @SerializedName("total")
        private String total;
        @Expose
        @SerializedName("date_time_string")
        private String dateTimeString;

        public ActivityList() {
        }

        public ActivityList(String id, String iconName, String iconPath, String title, String subtitle, String date, String status, String trxType, String referenceNumber) {
            this.id = id;
            this.iconName = iconName;
            this.iconPath = iconPath;
            this.title = title;
            this.subtitle = subtitle;
            this.date = date;
            this.status = status;
            this.trxType = trxType;
            this.referenceNumber = referenceNumber;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getIconName() {
            return iconName;
        }

        public void setIconName(String iconName) {
            this.iconName = iconName;
        }

        public String getIconPath() {
            return iconPath;
        }

        public void setIconPath(String iconPath) {
            this.iconPath = iconPath;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getSubtitle() {
            return subtitle;
        }

        public void setSubtitle(String subtitle) {
            this.subtitle = subtitle;
        }

        public String getDate() {
            return date;
        }

        public void setDate(String date) {
            this.date = date;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getTrxType() {
            return trxType;
        }

        public void setTrxType(String trxType) {
            this.trxType = trxType;
        }

        public String getReferenceNumber() {
            return referenceNumber;
        }

        public void setReferenceNumber(String referenceNumber) {
            this.referenceNumber = referenceNumber;
        }
        public String getTrxStatus() {
            return trxStatus;
        }

        public String getTotal() {
            return total;
        }

        public String getDateTimeString() {
            return dateTimeString;
        }
    }
}
package id.co.bri.brimo.ui.fragments.bukarekening

import android.Manifest
import android.app.SearchManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.provider.Settings
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.DialogFragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.bukarekening.ListLokasiKantorNewSkinAdapter
import id.co.bri.brimo.adapters.bukarekening.ListLokasiKantorRevAdapter
import id.co.bri.brimo.contract.IPresenter.general.IGeneralPilihKantorPresenter
import id.co.bri.brimo.contract.IView.general.IGeneralPilihKantorView
import id.co.bri.brimo.databinding.ActivityPilihanKantorBinding
import id.co.bri.brimo.databinding.FragmentBottomSheetPilihRekeningBinding
import id.co.bri.brimo.databinding.FragmentPilihKantorNewskinBinding
import id.co.bri.brimo.domain.helpers.GpsTracker
import id.co.bri.brimo.models.ParameterPilihKantorModel
import id.co.bri.brimo.models.apimodel.request.LocationRequest
import id.co.bri.brimo.models.apimodel.request.SearchLocationRequest
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse
import id.co.bri.brimo.models.apimodel.response.GeneralResponse
import id.co.bri.brimo.models.apimodel.response.InquiryGeneralOpenAccountResponse
import id.co.bri.brimo.models.apimodel.response.InquiryOpenRencanaResponse
import id.co.bri.brimo.models.apimodel.response.ListKantorResponse
import id.co.bri.brimo.models.apimodel.response.junio.FormOpenJunioResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.britamajunio.FormBritamaJunioActivity
import id.co.bri.brimo.ui.activities.openaccount.InquiryGeneralOpenAccountActivity
import id.co.bri.brimo.ui.activities.simpedes.InquirySimpedesOpenActivity
import id.co.bri.brimo.ui.fragments.rdn.CustomBottomDialogFragment
import javax.inject.Inject

class BottomSheetPilihKantorFragment : BottomSheetDialogFragment(),
    IGeneralPilihKantorView,
    View.OnClickListener,
    CustomBottomDialogFragment.DialogDefaultListener,
    ListLokasiKantorNewSkinAdapter.OnClickItem {

    private var _binding: FragmentPilihKantorNewskinBinding? = null
    private val binding get() = _binding!!

    private lateinit var lokasiKantorAdapter: ListLokasiKantorNewSkinAdapter
    private var skeletonScreen: SkeletonScreen? = null
    private val officeList = mutableListOf<ListKantorResponse.Office>()

    private var inLatitude: String? = null
    private var inLongitude: String? = null
    private var address: String? = null
    private var querySearch = ""

    private var customBottomDialogFragment: CustomBottomDialogFragment? = null

    @Inject
    lateinit var presenter: IGeneralPilihKantorPresenter<IGeneralPilihKantorView>

    companion object {
        private lateinit var parameterModel: ParameterPilihKantorModel
        private lateinit var branch: ListKantorResponse.Office

        fun newInstance(
            model: ParameterPilihKantorModel,
            office: ListKantorResponse.Office
        ): BottomSheetPilihKantorFragment {
            parameterModel = model
            branch = office
            return BottomSheetPilihKantorFragment()
        }
    }

    interface OnOfficeSelectedListener {
        fun onOfficeSelected(officeSelected: ListKantorResponse.Office)
    }

    var onOfficeSelectedListener: OnOfficeSelectedListener? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPilihKantorNewskinBinding.inflate(inflater, container, false)
        setStyle(DialogFragment.STYLE_NORMAL, R.style.CustomBottomSheetDialogTheme)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog?.setOnShowListener {
            val bottomSheet = dialog?.findViewById(R.id.design_bottom_sheet) as FrameLayout
            bottomSheet.background = ContextCompat.getDrawable(requireContext(), R.drawable.rounded_dialog_newskin)
            val behavior = BottomSheetBehavior.from(bottomSheet)
            behavior.state = BottomSheetBehavior.STATE_EXPANDED
        }

        injectDependency()
        setupRecyclerView()
        getLocation()
        setupSearchView()
        setupSearchViewStyle()
    }

    private fun injectDependency() {
        (requireActivity() as? BaseActivity)?.activityComponent?.inject(this)
        presenter.setView(this)
        presenter.setUrl(parameterModel.urlOwnLocation)
        presenter.setSearchUrl(parameterModel.urlSearchLocation)
        presenter.start()
    }

    private fun setupRecyclerView() {
        lokasiKantorAdapter = ListLokasiKantorNewSkinAdapter(officeList, this)
        binding.recyclerView2.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = lokasiKantorAdapter
        }

        skeletonScreen = Skeleton.bind(binding.recyclerView2)
            .adapter(lokasiKantorAdapter)
            .shimmer(true)
            .angle(10)
            .frozen(false)
            .duration(1200)
            .load(R.layout.item_skeleton_pilih_kantor_newskin)
            .show()
    }

    private fun setupSearchView() {
        binding.ivClose.setOnClickListener {
            dismiss()
        }
        binding.txtTitle.text = ContextCompat.getString(requireActivity(), R.string.pilih_kantor_bri)
        val searchManager =
            requireContext().getSystemService(Context.SEARCH_SERVICE) as? SearchManager
        binding.searchviewBukarek.apply {
            setSearchableInfo(searchManager?.getSearchableInfo(requireActivity().componentName))
            setOnQueryTextListener(object : androidx.appcompat.widget.SearchView.OnQueryTextListener {
                override fun onQueryTextSubmit(query: String?): Boolean {
                    querySearch = query.orEmpty()
                    binding.lyNotFound.visibility = View.GONE
                    binding.llKantorTerdekat.visibility = View.VISIBLE
                    presenter.sendSearchLocation(createSearchLocationRequest())
                    skeletonScreen?.show()
                    return false
                }

                override fun onQueryTextChange(newText: String?): Boolean {
                    address = newText
                    return false
                }
            })
        }
    }

    private fun setupSearchViewStyle() {
        val editText = binding.searchviewBukarek.findViewById<EditText>(androidx.appcompat.R.id.search_src_text)
        editText.setHintTextColor(resources.getColor(R.color.black3))
        editText.typeface = ResourcesCompat.getFont(requireContext(), R.font.avenir_next_medium)
        editText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14f)
    }

    private fun getLocation() {
        val gpsTracker = GpsTracker(requireContext())
        if (gpsTracker.canGetLocation()) {
            inLatitude = gpsTracker.latitude.toString()
            inLongitude = gpsTracker.longitude.toString()
            presenter.sendLokasiSendiri(createLocationRequest())
        } else {
            startActivity(Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS))
        }
    }

    override fun onRootedDevice() {
        //do nothing
    }

    override fun showProgress() {
        //do nothing
    }

    override fun hideProgress() {
        //do nothing
    }

    override fun onSessionEnd(message: String?) {
        //do nothing
    }

    override fun onException(message: String?) {
        //do nothing
    }

    override fun onExceptionRevamp(message: String?) {
        //do nothing
    }

    override fun onException06(response: ExceptionResponse?) {
        //do nothing
    }

    override fun onException99(message: String?) {
        //do nothing
    }

    override fun onExceptionFO(response: EmptyStateResponse?) {
        //do nothing
    }

    override fun onExceptionLimitExceed(response: GeneralResponse?) {
        //do nothing
    }

    override fun onExceptionNoBackAction(message: String?) {
        //do nothing
    }

    override fun onExceptionStatusNotMatch() {
        //do nothing
    }

    override fun onSuccessLocationDefault(kantorResponse: ListKantorResponse) {
        skeletonScreen?.hide()
        lokasiKantorAdapter.setItems(kantorResponse.officeList)
        binding.apply {
            recyclerView2.visibility = View.VISIBLE
            lyNotFound.visibility = View.GONE
            llKantorTerdekat.visibility = View.VISIBLE
        }
    }

    override fun onFailedLocation(msg: String?) {
        showNotFoundState()
    }

    private fun showNotFoundState() {
        skeletonScreen?.hide()
        binding.lyNotFound.visibility = View.VISIBLE
        binding.llKantorTerdekat.visibility = View.GONE

        val msgTitle: String
        val msgDesc: String

        if (requireContext().checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            msgTitle = getString(R.string.title_internet_problem)
            msgDesc = getString(R.string.desc_internet_problem)
        } else if (!querySearch.isNullOrEmpty()) {
            msgTitle = getString(R.string.title_kantor_not_found)
            msgDesc = getString(R.string.desc_kantor_not_found, querySearch)
        } else {
            msgTitle = getString(R.string.title_internet_problem)
            msgDesc = getString(R.string.desc_internet_problem)
        }

        binding.apply {
            tvDataTidakDitemukan.text = msgTitle
            tvDescNotFound.text = msgDesc
        }
    }

    override fun createLocationRequest(): LocationRequest {
        return LocationRequest(inLatitude, inLongitude)
    }

    override fun createSearchLocationRequest(): SearchLocationRequest {
        return SearchLocationRequest(inLatitude, inLongitude, address)
    }

    override fun onException12(msg: String?) {
        showComingSoonDialog()
    }

    private fun showComingSoonDialog() {
        customBottomDialogFragment = CustomBottomDialogFragment(
            requireContext(),
            "Segera Hadir",
            "Kami tengah menyiapkan fitur ini...",
            "Ok",
            "image_coming_soon",
            false,
            this
        )
        customBottomDialogFragment?.show(childFragmentManager, "")
    }

    override fun onSuccessInquiryS3F(response: InquiryOpenRencanaResponse) {
        InquirySimpedesOpenActivity.launchIntent(requireActivity(), Gson().toJson(response))
    }

    override fun onSuccesGetDataForm(response: FormOpenJunioResponse, branchCode: String) {
        FormBritamaJunioActivity.launchIntent(requireActivity(), response, parameterModel.productType, branchCode)
    }

    override fun onSuccessInquiryGeneralOpen(response: InquiryGeneralOpenAccountResponse) {
        InquiryGeneralOpenAccountActivity.launchIntent(requireActivity(), Gson().toJson(response), parameterModel.name)
    }

    override fun onClick(view: View?) {}

    override fun onClickDialog() {
        dismiss()
    }

    override fun onDestroyView() {
        presenter.stop()
        _binding = null
        super.onDestroyView()
    }

    override fun onClickKantor(office: ListKantorResponse.Office?) {
        if (office != null) {
            onOfficeSelectedListener?.onOfficeSelected(office)
        }
        dismiss()
    }

    override fun onStart() {
        super.onStart()

        val bottomSheet = dialog?.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
        bottomSheet?.let { sheet ->
            val behavior = com.google.android.material.bottomsheet.BottomSheetBehavior.from(sheet)
            behavior.state = com.google.android.material.bottomsheet.BottomSheetBehavior.STATE_EXPANDED

            sheet.layoutParams.height = ViewGroup.LayoutParams.MATCH_PARENT

            sheet.setBackgroundResource(R.drawable.rounded_dialog_newskin)
            sheet.clipToOutline = true

            (sheet.parent as? ViewGroup)?.clipChildren = true
            (sheet.parent as? ViewGroup)?.clipToPadding = true
        }
    }
}

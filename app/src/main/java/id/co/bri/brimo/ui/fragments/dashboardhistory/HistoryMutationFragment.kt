package id.co.bri.brimo.ui.fragments.dashboardhistory

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.SystemClock
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.bumptech.glide.Glide
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.ListBulanMutasiAdapter
import id.co.bri.brimo.adapters.ListMutasiAdapter
import id.co.bri.brimo.adapters.bukarekeningnewskin.FilterMonthNewSkinAdapter
import id.co.bri.brimo.contract.IPresenter.mutasi.IMutasiPresenter
import id.co.bri.brimo.contract.IView.mutasi.IMutasiView
import id.co.bri.brimo.databinding.FragmentMutationHistoryBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.AnimateHelper
import id.co.bri.brimo.domain.helpers.AnimateHelper.AnimateHelperListener
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin.formatDateToFullDay
import id.co.bri.brimo.domain.helpers.calendar.makeGone
import id.co.bri.brimo.domain.helpers.calendar.makeVisible
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.TransactionTypeModel
import id.co.bri.brimo.models.YearModel
import id.co.bri.brimo.models.apimodel.request.MutationDateRangeFilterRequest
import id.co.bri.brimo.models.apimodel.request.MutationFilterRequest
import id.co.bri.brimo.models.apimodel.request.MutationMonthFilterRequest
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse
import id.co.bri.brimo.models.apimodel.response.InquiryFiveMutasiResponse
import id.co.bri.brimo.models.apimodel.response.ListRekeningResponse
import id.co.bri.brimo.models.apimodel.response.MutasiResponse
import id.co.bri.brimo.ui.activities.DashboardIBActivity
import id.co.bri.brimo.ui.activities.MutationFilterActivity
import id.co.bri.brimo.ui.activities.StickHeaderItemDecoration
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.detailkartunewskin.HomeCardActivity
import id.co.bri.brimo.ui.fragments.NewSkinBaseFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.BottomSheetSourceOfAccountFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmationDismissEvent
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment.showDialogInformation1Button
import javax.inject.Inject

class HistoryMutationFragment : NewSkinBaseFragment(), IMutasiView, FilterMonthNewSkinAdapter.OnItemClickListener,
    SwipeRefreshLayout.OnRefreshListener, AnimateHelperListener{

    private lateinit var binding: FragmentMutationHistoryBinding
    private var monthAdapter: FilterMonthNewSkinAdapter? = null
    private var mutasiResponses: MutableList<MutasiResponse> = mutableListOf()
    private var accountList: MutableList<ListRekeningResponse.Account> = mutableListOf()
    private var listBulanMutasiAdapter: ListBulanMutasiAdapter? = null
    private var mutasiAdapter: ListMutasiAdapter? = null
    private var recyclerItemDecoration: StickHeaderItemDecoration? = null
    private var mTotalScrolled = 0
    private var transactionType: ArrayList<TransactionTypeModel> = ArrayList()
    private var yearList: ArrayList<YearModel> = ArrayList()
    private var model: AccountModel? = null
    private var mListAccountModel: ArrayList<AccountModel> = ArrayList()
    private val animateHelper = AnimateHelper(this)
    private var mutationFilterRequest: MutationFilterRequest? = null
    private var mutationMonthFilterRequest: MutationMonthFilterRequest? = null
    private var mutationDateRangeFilterRequest: MutationDateRangeFilterRequest? = null
    private var skeletonScreenMutasi: SkeletonScreen? = null

    var rangeFilter: String? = null
    var transactionTypeId: String? = null
    private var transactionTypeName: String? = null
    var years: String? = null
    var monthId: String? = null
    var accountNumber: String = ""
    var accountName: String? = null
    private var accountMainNumber: String? = null
    private var accountMainName: String? = null
    private var accountMainImage: String? = null
    var month: String? = null
    var startDate: String? = null
    var endDate: String? = null
    private var isDecorationAdded = false
    private var hasShownErrorDialog = false
    private var isSkeletonShown = false
    private var isResetFilter = false
    private var titleEmptyState = ""
    private var descEmptyState = ""
    private var imgDrawable: Int? = null
    private var isClickedSOA = false

    @Inject
    lateinit var presenter: IMutasiPresenter<IMutasiView>

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        presenter.setView(this)
        presenter.start()
        binding.content.post {
            initSkeleton()
        }
        callService()
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentMutationHistoryBinding.inflate(inflater, container, false)
        injectDependency()
        initiateBulanAdapter()
        setupView()
        return binding.root
    }


    private fun initSkeleton() {

        skeletonScreenMutasi =
            Skeleton.bind(binding.content).shimmer(true).angle(20).duration(1200)
                .color(R.color.white).load(R.layout.item_skeleton_mutation_history).show()

    }

    private fun setupView() {

        binding.swipeMutation.setOnRefreshListener(this)

        binding.rvItemMutasi.addOnScrollListener(object : RecyclerView.OnScrollListener() {

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                mTotalScrolled += dy
            }
        })

        binding.rvItemMutasi.viewTreeObserver.addOnScrollChangedListener {
            if (getScrollForRecycler() == 0) {
                binding.swipeMutation.setEnabled(true)
            } else {
                binding.swipeMutation.setEnabled(false)
            }
        }

        binding.tvChip.text = getString(R.string.reset)

        setupClickAction()
        setupFilter()

    }

    private fun callService() {

        presenter.setRekeningUrl(GeneralHelper.getString(R.string.url_v5_account_list_all))
        presenter.setUrlGetMutationDateRange(GeneralHelper.getString(R.string.url_v3_mutation_date_range))
        presenter.setAccount(accountNumber)
        presenter.getDataRekening()

    }

    private fun getFirstMutasi() {
        presenter.setAccount(accountNumber)
        presenter.setUrlGetMutationLastFive(GeneralHelper.getString(R.string.url_v3_mutation_last_five))
        presenter.getMutationLastFive()
    }

    private fun getScrollForRecycler(): Int {
        return mTotalScrolled
    }

    private fun setupFilter() {

        val filterMonthList = listOf("Jan", "Feb", "Mar", "Apr", "Mei", "Jun", "Jul", "Agu", "Sep", "Okt", "Nov", "Des")
        monthAdapter = FilterMonthNewSkinAdapter(this, filterMonthList, requireActivity())
        binding.rvMonth.setHasFixedSize(true)
        binding.rvMonth.layoutManager = LinearLayoutManager(requireActivity(), RecyclerView.HORIZONTAL, false)
        binding.rvMonth.adapter = monthAdapter

    }

    private fun setupClickAction() {

        binding.viewFilterIcon.setOnClickListener {

            MutationFilterActivity.launchIntent(resultLauncher, requireActivity(), mListAccountModel, transactionType, yearList,
                rangeFilter, transactionTypeId, monthId, years, startDate, endDate, false)

        }

        binding.viewReset.setOnClickListener {
            isResetFilter = true
            presenter.setAccount(accountNumber)
            rangeFilter = null
            reCallService()
            isResetFilter = false
        }

        binding.viewFailedSoa.viewRefresh.setOnClickListener {
            callService()
        }

        binding.viewConnectCard.setOnClickListener {

            val fragment = BottomSheetSourceOfAccountFragment.newInstance(mListAccountModel)
            fragment.onSelectedListener = object : BottomSheetSourceOfAccountFragment.OnClickedSourceOfAccount {
                override fun onClicked(account: AccountModel) {
                    binding.swipeMutation.isRefreshing = false
                    accountNumber = account.acoount
                    presenter.setAccount(accountNumber)
                    binding.tvAccountName.makeVisible()

                    isClickedSOA = account.isDefault != 1

                    if (account.alias?.isNotEmpty() == true) {
                        accountName = account.alias
                        binding.tvAccountName.text = accountName
                    } else {
                        accountName = account.name
                        binding.tvAccountName.text = account.name
                    }
                    binding.tvAccountNumber.text = GeneralHelperNewSkin.formatPhoneNumber(accountNumber)

                    val image = if (account.imageCardMiniPath.isNullOrEmpty()) {
                        R.drawable.ic_placeholder_ns
                    } else {
                        account.imageCardMiniPath
                    }

                    Glide.with(requireActivity())
                        .load(image)
                        .fitCenter()
                        .placeholder(R.drawable.ic_placeholder_ns)
                        .error(R.drawable.ic_placeholder_ns)
                        .into(binding.ivCardLogo)

                    initSkeleton()
                    setResetFilter()

                }
            }
            fragment.show(requireActivity().supportFragmentManager, Constant.BOTTOMSHEET_SOURCE_OF_ACCOUNT)

        }

    }

    private fun injectDependency() {
        getActivityComponent().inject(this)

    }

    private fun initiateBulanAdapter() {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return
        }

        binding.rvItemMutasi.setLayoutManager(LinearLayoutManager(requireActivity(), RecyclerView.VERTICAL, false))
        binding.rvItemMutasi.smoothScrollToPosition(0)
        listBulanMutasiAdapter = ListBulanMutasiAdapter(mutasiResponses, mutasiAdapter, requireActivity())
        binding.rvItemMutasi.setAdapter(listBulanMutasiAdapter)

        recyclerItemDecoration = StickHeaderItemDecoration(
            requireActivity(), resources.getDimensionPixelSize(R.dimen.size_16dp),
            70,
            false,
            getSectionCallback(mutasiResponses)
        )
    }

    private fun isAccountNumberEmpty(): Boolean {
        return accountNumber.isNullOrEmpty()
    }

    private fun getSectionCallback(mutasi: MutableList<MutasiResponse>): StickHeaderItemDecoration.SectionCallback {
        return object : StickHeaderItemDecoration.SectionCallback {
            override fun isSection(position: Int): Boolean {
                val currentDate = mutasi.getOrNull(position)?.dateString.orEmpty()
                val previousDate = mutasi.getOrNull(position - 1)?.dateString.orEmpty()
                return position == 0 || currentDate != previousDate
            }

            override fun getSectionHeaderName(pos: Int): String {
                return formatDateToFullDay(mutasi.getOrNull(pos)?.date.orEmpty(), "id")
            }
        }
    }

    private fun getListMutation() {
        when (rangeFilter) {
            Constant.TODAY_FILTER, Constant.SEVEN_DAY_FILTER -> {
                val transactionType = transactionTypeId ?: Constant.TRANSACTION_TYPE_ALL
                val mutationFilterRequest = MutationFilterRequest(rangeFilter, accountNumber, transactionType)
                presenter.getMutationRange(mutationFilterRequest, null, null)
            }

            Constant.MONTH_FILTER -> {
                val transactionType = transactionTypeId ?: Constant.TRANSACTION_TYPE_ALL
                val mutationMonthFilterRequest = MutationMonthFilterRequest(rangeFilter, years, monthId, accountNumber, transactionType)
                presenter.getMutationRange(null, mutationMonthFilterRequest, null)
            }

            Constant.RANGE_FILTER -> {
                val transactionType = transactionTypeId ?: Constant.TRANSACTION_TYPE_ALL
                val mutationDateRangeFilterRequest = MutationDateRangeFilterRequest(rangeFilter, startDate, endDate, accountNumber, transactionType)
                presenter.getMutationRange(null, null, mutationDateRangeFilterRequest)
            }
        }
    }

    private val resultLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val data = result.data
            if (data != null) {
                if (data.getStringExtra(Constant.FILTER) != null) {
                    rangeFilter = data.getStringExtra(Constant.FILTER)
                    transactionTypeId = data.getStringExtra(Constant.TRANSACTION_TYPE_ID)
                    transactionTypeName = data.getStringExtra(Constant.TRANSACTION_TYPE_NAME)
                    getMutationWithFilter(data)
                } else {
                    setResetFilter()
                }
            } else {
                setResetFilter()
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun getMutationWithFilter(data: Intent) {
        when {
            rangeFilter.equals(Constant.TODAY_FILTER, ignoreCase = true) ||
                    rangeFilter.equals(Constant.SEVEN_DAY_FILTER, ignoreCase = true) -> {

                mutationFilterRequest = if (transactionTypeId == null) {
                    MutationFilterRequest(rangeFilter, accountNumber, Constant.TRANSACTION_TYPE_ALL)
                } else {
                    MutationFilterRequest(rangeFilter, accountNumber, transactionTypeId)
                }

                presenter.getMutationRange(mutationFilterRequest, null, null)
            }

            rangeFilter.equals(Constant.MONTH_FILTER, ignoreCase = true) -> {
                years = data.getStringExtra(Constant.YEAR)
                monthId = data.getStringExtra(Constant.MONTH)
                month = data.getStringExtra(Constant.MONTH_TEXT)

                mutationMonthFilterRequest = if (transactionTypeId == null) {
                    MutationMonthFilterRequest(rangeFilter, years, monthId, accountNumber, Constant.TRANSACTION_TYPE_ALL)
                } else {
                    MutationMonthFilterRequest(rangeFilter, years, monthId, accountNumber, transactionTypeId)
                }

                presenter.getMutationRange(null, mutationMonthFilterRequest, null)
            }

            rangeFilter.equals(Constant.RANGE_FILTER, ignoreCase = true) -> {
                startDate = data.getStringExtra(Constant.TAG_START_DATE)
                endDate = data.getStringExtra(Constant.TAG_END_DATE)

                mutationDateRangeFilterRequest = if (transactionTypeId == null) {
                    MutationDateRangeFilterRequest(rangeFilter, startDate, endDate, accountNumber, Constant.TRANSACTION_TYPE_ALL)
                } else {
                    MutationDateRangeFilterRequest(rangeFilter, startDate, endDate, accountNumber, transactionTypeId)
                }

                presenter.getMutationRange(null, null, mutationDateRangeFilterRequest)
            }
        }

        binding.rvMonth.makeGone()
        binding.ivBullet.makeVisible()
        binding.viewReset.makeVisible()

    }

    private fun showEmptyState() {

        binding.rvItemMutasi.makeGone()
        binding.viewEmpty.root.makeVisible()
        binding.viewEmpty.imgEmpty.setImageDrawable(imgDrawable?.let {
            ContextCompat.getDrawable(requireActivity(),
                it
            )
        })
        binding.viewEmpty.tvTitleEmpty.text = titleEmptyState
        binding.viewEmpty.tvDescEmpty.text = descEmptyState

    }

    private fun setupField() {

        model = AccountModel()
        mListAccountModel.clear()
        mListAccountModel.addAll(fetchAccountModel())

        val model = mListAccountModel.firstOrNull { it.isDefault == 1 }

        if (model != null && model.isDefault  == 1){
            binding.viewFailedSoa.viewConnectCard.makeGone()
            binding.viewConnectCard.makeVisible()
        } else {
            binding.viewFailedSoa.viewConnectCard.makeVisible()
            binding.viewConnectCard.makeGone()
        }

        if (mListAccountModel.size == 1) {
            binding.ivArrow.makeGone()
            binding.viewConnectCard.isClickable = false
        } else {
            binding.ivArrow.makeVisible()
            binding.viewConnectCard.isClickable = true
        }

        accountNumber = model?.acoount.orEmpty()
        accountName = model?.alias.orEmpty()
        if (accountName?.isBlank() == true) {
            accountName = model?.name.orEmpty()
        }

        val image = if (model?.imageCardMiniPath.isNullOrEmpty()) {
            R.drawable.ic_placeholder_ns
        } else {
            model?.imageCardMiniPath
        }

        Glide.with(requireActivity())
            .load(image)
            .fitCenter()
            .placeholder(R.drawable.ic_placeholder_ns)
            .error(R.drawable.ic_placeholder_ns)
            .into(binding.ivCardLogo)

        if (accountName?.isNotEmpty() == true) {
            binding.tvAccountName.text = accountName
        } else {
            binding.tvAccountName.text = model?.name
        }

        binding.tvAccountNumber.text = GeneralHelperNewSkin.formatPhoneNumber(accountNumber)

        accountMainNumber = accountNumber
        accountMainName = accountName
        accountMainImage = model?.imageCardMiniPath

    }

    private fun fetchAccountModel(): MutableList<AccountModel> {
        val accountModelList: MutableList<AccountModel> = mutableListOf()

        for (i in accountList.indices) {
            accountModelList.add(
                AccountModel(
                    accountList[i].account,
                    accountList[i].accountString,
                    accountList[i].name,
                    accountList[i].currency,
                    accountList[i].cardNumber,
                    accountList[i].productType,
                    accountList[i].accountType,
                    accountList[i].scCode,
                    accountList[i].default,
                    accountList[i].alias,
                    accountList[i].productImageUrl,
                    accountList[i].imageCardMiniPath
                )
            )
        }
        return accountModelList
    }

    private fun showError() {

        if (hasShownErrorDialog) return

        hasShownErrorDialog = true

        val firstBtnFunction = Runnable {
            hasShownErrorDialog = false
            callService()
            reCallService()
        }

        showDialogInformation1Button(
            requireActivity().supportFragmentManager,
            "",
            Constant.IC_SAD_NEW_NS,
            getString(R.string.failed_load_page_title),
            getString(R.string.failed_load_page_subtitle),
            createKotlinFunction0(firstBtnFunction),
            true,
            getString(R.string.retry),
            true
        )

    }

    override fun onSessionEnd(message: String?) {
        (requireActivity() as DashboardIBActivity).onSessionEnd(message)
        presenter.stop()
    }

    override fun onItemClick(month: String) {

    }

    override fun onExceptionRevamp(message: String?) {
        binding.swipeMutation.isRefreshing = false
        titleEmptyState = getString(R.string.txt_failed_history_trans_title)
        descEmptyState = getString(R.string.txt_failed_history_trans_desc)
        imgDrawable = R.drawable.ic_sad_new_ns
        showEmptyState()
        showError()
        binding.viewFailedSoa.viewConnectCard.makeVisible()
        binding.viewConnectCard.makeGone()
    }

    override fun onException(message: String) {
        binding.swipeMutation.isRefreshing = false
        titleEmptyState = getString(R.string.txt_no_trans_title)
        descEmptyState = getString(R.string.txt_no_trans_desc_empty)
        imgDrawable = R.drawable.empty_state_new_ns
        showEmptyState()
        showError()
    }

    override fun onException12(message: String) {
        titleEmptyState = getString(R.string.txt_failed_history_trans_title)
        descEmptyState = getString(R.string.txt_failed_history_trans_desc)
        imgDrawable = R.drawable.ic_sad_new_ns
        showEmptyState()
        binding.swipeMutation.isRefreshing = false
        recyclerItemDecoration?.let { binding.rvItemMutasi.removeItemDecoration(it) }
        showError()
    }

    override fun onException93(message: String) {
        binding.swipeMutation.isRefreshing = false
        titleEmptyState = getString(R.string.txt_no_trans_title)
        descEmptyState = getString(R.string.txt_no_trans_desc_empty)
        imgDrawable = R.drawable.empty_state_new_ns
        showEmptyState()
        recyclerItemDecoration?.let { binding.rvItemMutasi.removeItemDecoration(it) }
        showError()
    }

    override fun onException99(message: String?) {
        binding.swipeMutation.isRefreshing = false
        showError()
        titleEmptyState = getString(R.string.txt_no_trans_title)
        descEmptyState = getString(R.string.txt_no_trans_desc_empty)
        imgDrawable = R.drawable.empty_state_new_ns
        showEmptyState()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onSuccessMutasiFive(response: InquiryFiveMutasiResponse) {
        binding.viewEmpty.root.visibility = View.GONE
        binding.rvItemMutasi.visibility = View.VISIBLE
        binding.swipeMutation.isRefreshing = false
        mutasiResponses.clear()
        response.let { mutasiResponses.addAll(it.mutation) }
        response.let { transactionType = it.listTypeTransaction }
        listBulanMutasiAdapter?.notifyDataSetChanged()
        yearList = response.listYear
        if (!isAccountNumberEmpty()) {
            binding.viewFilterIcon.isClickable = true
        }
    }

    override fun isHideSkeleton(hide: Boolean) {
        try {
            if (hide) {
                if (isSkeletonShown) {
                    skeletonScreenMutasi?.hide()
                    isSkeletonShown = false
                }
                if (!isAccountNumberEmpty()) {
                    binding.viewFilterIcon.isClickable = true
                }
                if (!isDecorationAdded) {
                    recyclerItemDecoration?.let { binding.rvItemMutasi.addItemDecoration(it) }
                    isDecorationAdded = true
                }
                binding.swipeMutation.isRefreshing = false
            } else {
                skeletonScreenMutasi?.show()
                isSkeletonShown = true
                binding.viewFilterIcon.isClickable = false
                if (isDecorationAdded) {
                    recyclerItemDecoration?.let { binding.rvItemMutasi.removeItemDecoration(it) }
                    isDecorationAdded = false
                }
                binding.swipeMutation.isRefreshing = false
            }
        } catch (_: Exception) {

        }

    }

    override fun isShowData(show: Boolean) {
        if (!show) {
            binding.rvItemMutasi.makeGone()
        } else {
            animateHelper.onAnimatorShow(binding.rvItemMutasi, true, Constant.REQUEST_MUTASI)
        }
        binding.swipeMutation.isRefreshing = false
    }

    override fun onSuccessGetAccount(account: ArrayList<ListRekeningResponse.Account>) {
        binding.viewFailedSoa.viewConnectCard.makeGone()
        binding.viewConnectCard.makeVisible()
        accountList.clear()
        accountList.addAll(account)
        binding.swipeMutation.isRefreshing = false
        setupField()
        presenter.setAccount(accountNumber)
        getFirstMutasi()
    }

    override fun showEmptyState(response: EmptyStateResponse) {
        binding.swipeMutation.isRefreshing = false
        titleEmptyState = getString(R.string.txt_no_trans_title)
        descEmptyState = getString(R.string.txt_no_trans_desc)
        imgDrawable = R.drawable.empty_state_new_ns
        showEmptyState()
        if (response.listYear != null && response.listTypeTransaction != null) {
            transactionType = response.listTypeTransaction
            yearList = response.listYear
        }
        if (!isAccountNumberEmpty()) {
            binding.viewFilterIcon.isClickable = true
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onSuccessGetMutation(mutasiResponse: InquiryFiveMutasiResponse) {
        binding.rvItemMutasi.makeVisible()
        binding.viewEmpty.root.makeGone()
        binding.swipeMutation.isRefreshing = false
        mutasiResponses.clear()
        mutasiResponses.addAll(mutasiResponse.mutation)
        listBulanMutasiAdapter?.notifyDataSetChanged()
    }

    private fun setResetFilter() {
        resetViewFilter()
        accountName = null
//        accountNumber = accountMainNumber
        rangeFilter = null
        transactionTypeId = null
        transactionTypeName = null
        monthId = null
        years = null
        startDate = null
        endDate = null
        getFirstMutasi()
    }

    private fun reCallService() {

        if (rangeFilter == null) {
            rangeFilter = null
            accountName = null
            transactionTypeId = null
            resetViewFilter()
            getFirstMutasi()
        } else {
            getListMutation()
        }

    }

    private fun resetViewFilter() {

//        if (isResetFilter) {
//            binding.tvAccountName.text = accountMainName
//            binding.tvAccountNumber.text = accountMainNumber
//
//            val image = if (accountMainImage.isNullOrEmpty()) {
//                R.drawable.dummy_britama
//            } else {
//                accountMainImage
//            }
//            Glide.with(requireActivity())
//                .load(image)
//                .fitCenter()
//                .into(binding.ivCardLogo)
//        }

        binding.ivBullet.makeGone()
        binding.viewReset.makeGone()

    }

    override fun onRefresh() {
        initSkeleton()
        recyclerItemDecoration?.let { binding.rvItemMutasi.removeItemDecoration(it) }
        presenter.setAccount(accountNumber)
        if (!isClickedSOA) {
            presenter.getDataRekening()
        }
        reCallService()
    }

    override fun onResume() {
        super.onResume()
        hasShownErrorDialog = false
    }

    override fun onAnimatorShowEnd(tagId: String) {

    }

    override fun onAnimatorFadeEnd(tagId: String) {
    }

    override fun onDestroy() {
        super.onDestroy()
        mutasiResponses.clear()
        accountList.clear()
        mListAccountModel.clear()
    }

    companion object {
        @JvmStatic
        fun newInstance(): HistoryMutationFragment {
            val fragment = HistoryMutationFragment()
            return fragment
        }

    }

}
package id.co.bri.brimo.ui.fragments.bukaRekening

import android.os.Build
import android.os.Bundle
import android.text.Html
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.DetailTabunganAdapter
import id.co.bri.brimo.databinding.FragmentTabunganCardBinding
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.bukarekening.JenisTabunganResponse
import id.co.bri.brimo.ui.activities.bukarekening.ProductBriefBukaRekeningActivity
import id.co.bri.brimo.ui.fragments.BaseFragment
import id.co.bri.brimo.ui.fragments.SetCalendarFragment


/**
 * A simple [Fragment] subclass.
 * Use the [SetCalendarFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class TabunganCardFragment : BaseFragment(), View.OnClickListener {

    private var _binding: FragmentTabunganCardBinding? = null
    private val binding get() = _binding!!

    var skeletonScreen: SkeletonScreen? = null

    private var detailTabunganAdapter: DetailTabunganAdapter? = null
    private var onListener: DialogDefaultListener? = null
    private var listProduct: List<JenisTabunganResponse.Product>? = null
    private var position: Int = 0
    private var mProductType: String? = null
    private var mFromProductRecomendetion: Boolean = false

    companion object {
        private const val ARG_LIST_PRODUCT = "arg_list_product"
        private const val ARG_POSITION = "arg_position"
        private const val ARG_PRODUCT_TYPE = "arg_product_type"
        private const val ARG_FROM_RECOMMENDATION = "arg_from_recommendation"

        fun newInstance(
            listener: DialogDefaultListener,
            listProduct: List<JenisTabunganResponse.Product>,
            position: Int,
            productType: String?,
            fromRecommendation: Boolean
        ): TabunganCardFragment {
            val fragment = TabunganCardFragment()
            val args = Bundle().apply {
                putParcelableArrayList(ARG_LIST_PRODUCT, ArrayList(listProduct))
                putInt(ARG_POSITION, position)
                putString(ARG_PRODUCT_TYPE, productType)
                putBoolean(ARG_FROM_RECOMMENDATION, fromRecommendation)
            }
            fragment.arguments = args
            fragment.onListener = listener
            return fragment
        }
    }
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentTabunganCardBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initArguments()
        loadData()
    }

    private fun initArguments() {
        arguments?.let {
            listProduct = it.getParcelableArrayList(ARG_LIST_PRODUCT)
            position = it.getInt(ARG_POSITION)
            mProductType = it.getString(ARG_PRODUCT_TYPE)
            mFromProductRecomendetion = it.getBoolean(ARG_FROM_RECOMMENDATION)
        }
    }

    private fun loadData() {
        binding.llDetailTabungan.setOnClickListener(this)
        GeneralHelper.loadImageUrl(
            context,
            listProduct?.get(position)?.imageProductUrl ?: "",
            binding.ivTabungan,
            0,
            0
        )
        binding.tvDesc.text = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            Html.fromHtml(
                listProduct?.get(position)?.onboardingData?.description_html,
                Html.FROM_HTML_MODE_COMPACT
            )
        } else {
            Html.fromHtml(listProduct?.get(position)?.onboardingData?.description_html)
        }

        binding.tvFooter.text = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            Html.fromHtml(listProduct?.get(position)?.onboardingData?.footer, Html.FROM_HTML_MODE_COMPACT)
        } else {
            Html.fromHtml(listProduct?.get(position)?.onboardingData?.footer)
        }

        // recyclview data
        detailTabunganAdapter =
            listProduct?.get(position)?.onboardingData?.let { DetailTabunganAdapter(requireContext(), it.offers) }
        binding.rvTabungan.adapter = detailTabunganAdapter
        binding.rvTabungan.layoutManager =
            LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)

        if (userVisibleHint) {
            listProduct?.get(position)?.let { onListener?.onType(it, position) }
        }

    }

    interface DialogDefaultListener {
        fun onType(product: JenisTabunganResponse.Product, position: Int)
    }

    override fun onResume() {
        super.onResume()
    }

    override fun onClick(p0: View?) {
        listProduct?.let {
            ProductBriefBukaRekeningActivity.launchIntent(baseActivity,
                it, position)
        }
    }

    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        super.setUserVisibleHint(isVisibleToUser)
        if (view != null && isVisibleToUser) {
            loadData()
        }
    }

    fun showSkeleton() {
        skeletonScreen = Skeleton.bind(binding.llTabunganCard)
            .shimmer(true)
            .angle(20)
            .duration(1200).color(R.color.white)
            .load(R.layout.skeleton_tabungan)
            .show()
    }

    fun hideSkeleton() {
        skeletonScreen!!.hide()
    }

}
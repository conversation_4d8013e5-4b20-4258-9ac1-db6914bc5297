package id.co.bri.brimo.ui.activities.emas

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.SystemClock
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.DataviewRevampAdapter
import id.co.bri.brimo.databinding.ActivityReceiptGagalAftactivityBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.PendingResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.emas.ReceiptEmasResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.bukarekening.ReceiptTabunganActivity
import id.co.bri.brimo.ui.fragments.bukaRekening.BottomFragmentReceipt

class ReceiptGagalAFTActivity : BaseActivity(), View.OnClickListener {
    lateinit var binding : ActivityReceiptGagalAftactivityBinding
    var bottomFragmentReceipt: BottomFragmentReceipt? = null

    companion object{
        var dataMaster : ReceiptRevampResponse? = null
        var mFromCatatanAktivitas : Boolean? = false
        @JvmStatic
        fun launchIntent(caller: Activity, data : ReceiptRevampResponse, fromCatatanAktivitas : Boolean) {
            val intent = Intent(caller, ReceiptGagalAFTActivity::class.java)
            dataMaster = data
            mFromCatatanAktivitas = fromCatatanAktivitas
            caller.startActivityForResult(intent, Constant.REQ_BUKA_REKENING)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityReceiptGagalAftactivityBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupView(dataMaster!!)
    }

    private fun setupView(response: ReceiptRevampResponse){

        binding.tvTitleTime.text = response.transactionDate
        GeneralHelper.loadImageUrl(this, response.transactionImage, binding.ivProduct, R.drawable.bri, 0)

        binding.rvDetailReceipt.layoutManager = LinearLayoutManager(this)
        var adapter: DataviewRevampAdapter = DataviewRevampAdapter(response.mainDataView, this)
        binding.rvDetailReceipt.adapter = adapter

        GeneralHelper.setWebViewStandart(binding.webview, "", response.footer)

        if (mFromCatatanAktivitas!!){
            binding.btnRekeningBaru.text = GeneralHelper.getString(R.string.ok)
        }else{
            binding.btnRekeningBaru.text = GeneralHelper.getString(R.string.txt_lihat_tabungan_emas_baru)
        }

        binding.btnRekeningBaru.setOnClickListener(this)
        binding.tvDetailTabungan.setOnClickListener(this)
    }

    private fun responseDetail(data: ReceiptRevampResponse): ReceiptRevampResponse{
        var response: ReceiptRevampResponse = ReceiptRevampResponse()

        response.immediatelyFlag = data.immediatelyFlag
        response.amountDataView = data.amountDataView
        response.headerDataView = data.headerDataView
        response.transactionDataViewPending = data.transactionDataViewPending
        response.referenceDataView = data.referenceDataView
        response.totalDataView = data.totalDataView

        return response
    }

    override fun onClick(p0: View?) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000){
            return
        }
        mLastClickTime = SystemClock.elapsedRealtime()

        when(p0!!.id){
            R.id.btn_rekening_baru->{
                val i = Intent()
                setResult(RESULT_OK, i)
                finish()
            }
            R.id.tvDetailTabungan->{
                bottomFragmentReceipt = BottomFragmentReceipt.newInstance(responseDetail(dataMaster!!), true)
                bottomFragmentReceipt!!.show(supportFragmentManager, "")
            }
        }
    }

    override fun onBackPressed() {
        if (mFromCatatanAktivitas!!){
            super.onBackPressed()
        }else{
            val i = Intent()
            setResult(RESULT_FIRST_USER,i)
            finish()
        }
    }
}
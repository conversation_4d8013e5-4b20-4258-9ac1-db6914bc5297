package id.co.bri.brimo.models;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.NonNull;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import id.co.bri.brimo.models.apimodel.response.cc.DetailCcSofResponse;
import id.co.bri.brimo.models.apimodel.response.SaldoReponse;

/**
 * Created by FNS on 16/12/2019
 */
public class AccountModel implements Parcelable {
    @SerializedName("account")
    @Expose
    private String acoount;
    @SerializedName("account_string")
    @Expose
    private String acoountString;
    @SerializedName("name")
    @Expose
    private String name;
    @SerializedName("currency")
    @Expose
    private String currency;
    @SerializedName("card_number")
    @Expose
    private String cardNumber;
    @SerializedName("card_number_string")
    @Expose
    private String cardNumberString;
    @SerializedName("product_type")
    @Expose
    private String productType;
    @SerializedName("account_type")
    @Expose
    private String accountType;
    @SerializedName("sc_code")
    @Expose
    private String scCode;
    @SerializedName("default")
    @Expose
    private Integer isDefault;
    @SerializedName("minimum_balance")
    @Expose
    private Integer minimumBalance = 0;
    @SerializedName("finansial_status")
    @Expose
    private Integer finansialStatus;
    @SerializedName("alias")
    @Expose
    private String alias;
    @SerializedName("currency_type")
    @Expose
    private String currency_type;
    @SerializedName("icon_name")
    @Expose
    private String icon_name;
    @SerializedName("icon_path")
    @Expose
    private String icon_path;
    @SerializedName("image_path")
    @Expose
    private String imagePath;
    @SerializedName("image_name")
    @Expose
    private String imageName;
    @SerializedName("limit")
    @Expose
    private Long limit;
    @SerializedName("limit_string")
    @Expose
    private String limitString;
    @SerializedName("image_product_type")
    @Expose
    private String imageProductType;
    @SerializedName("nfc_type")
    @Expose
    private String nfcType;


    @SerializedName("on_hold")
    @Expose
    private Boolean onHold =null;


    @SerializedName("balance")
    @Expose
    private Double balance = null ;


    @SerializedName("balance_string")
    @Expose
    private String balanceString =null ;

    private boolean isSelected = false;

    private boolean isOver = false;

    private SaldoReponse saldoReponse;

    private DetailCcSofResponse detailCcSofResponse;

    @SerializedName("image_card_mini_name")
    @Expose
    private String imageCardMiniName;

    @SerializedName("image_card_mini_path")
    @Expose
    private String imageCardMiniPath;

    public AccountModel (String acoount, String acoountString, String name, String currency, String cardNumber, String productType, String accountType, String scCode, Integer isDefault){
        this.acoount = acoount;
        this.acoountString = acoountString;
        this.name = name;
        this.currency = currency;
        this.cardNumber = cardNumber;
        this.productType = productType;
        this.accountType = accountType;
        this.scCode = scCode;
        this.isDefault = isDefault;
    }

    public AccountModel (String account, String acoountString, String name, String currency, String cardNumber, String productType, String accountType, String scCode, Integer isDefault, String alias) {
        this.acoount = account;
        this.acoountString = acoountString;
        this.name = name;
        this.currency = currency;
        this.cardNumber = cardNumber;
        this.productType = productType;
        this.accountType = accountType;
        this.scCode = scCode;
        this.isDefault = isDefault;
        this.alias = alias;
    }

    public AccountModel (String account, String acoountString, String name, String currency, String cardNumber, String productType, String accountType, String scCode, Integer isDefault, String alias, String imagePath, String imageCardMiniPath) {
        this.acoount = account;
        this.acoountString = acoountString;
        this.name = name;
        this.currency = currency;
        this.cardNumber = cardNumber;
        this.productType = productType;
        this.accountType = accountType;
        this.scCode = scCode;
        this.isDefault = isDefault;
        this.alias = alias;
        this.imagePath = imagePath;
        this.imageCardMiniPath = imageCardMiniPath;
    }

    public AccountModel (String account, String acoountString, String name, String currency, String cardNumber, String cardNumberString, String productType, String accountType, String scCode, Integer isDefault, String alias, Integer minimumBalance, Long limit, String limitString, String imageName, String imagePath, String nfcType) {
        this.acoount = account;
        this.acoountString = acoountString;
        this.name = name;
        this.currency = currency;
        this.cardNumber = cardNumber;
        this.cardNumberString = cardNumberString;
        this.productType = productType;
        this.accountType = accountType;
        this.scCode = scCode;
        this.isDefault = isDefault;
        this.alias = alias;
        this.minimumBalance = minimumBalance;
        this.limit = limit;
        this.limitString = limitString;
        this.imageName = imageName;
        this.imagePath = imagePath;
        this.nfcType = nfcType;
    }

    public AccountModel(){

    }

    public AccountModel(String account, String accountString, String name, String currency, String cardNumber, String cardNumberString, String productType, String accountType, String scCode, Integer isDefault, String alias, Integer minimumBalance, String imagePath,String imageName, DetailCcSofResponse detailCcSofResponse, Long limit, String limitString) {
        this.acoount = account;
        this.acoountString = accountString;
        this.name = name;
        this.currency = currency;
        this.cardNumber = cardNumber;
        this.cardNumberString = cardNumberString;
        this.productType = productType;
        this.accountType = accountType;
        this.scCode = scCode;
        this.isDefault = isDefault;
        this.alias = alias;
        this.minimumBalance = minimumBalance;
        this.imagePath = imagePath;
        this.imageName = imageName;
        this.detailCcSofResponse = detailCcSofResponse;
        this.limit = limit;
        this.limitString = limitString;
    }


    public AccountModel(String acoount, String acoountString, String name, String currency, String cardNumber, String productType, String accountType, String scCode, Integer isDefault, Integer minimumBalance, Integer finansialStatus, String alias, SaldoReponse saldoReponse, String nfcType) {
        this.acoount = acoount;
        this.acoountString = acoountString;
        this.name = name;
        this.currency = currency;
        this.cardNumber = cardNumber;
        this.productType = productType;
        this.accountType = accountType;
        this.scCode = scCode;
        this.isDefault = isDefault;
        this.minimumBalance = minimumBalance;
        this.finansialStatus = finansialStatus;
        this.alias = alias;
        this.saldoReponse = saldoReponse;
        this.nfcType = nfcType;
    }

    protected AccountModel(Parcel in) {
        acoount = in.readString();
        acoountString = in.readString();
        name = in.readString();
        currency = in.readString();
        cardNumber = in.readString();
        cardNumberString = in.readString();
        productType = in.readString();
        accountType = in.readString();
        scCode = in.readString();
        if (in.readByte() == 0) {
            isDefault = null;
        } else {
            isDefault = in.readInt();
        }
        if (in.readByte() == 0) {
            minimumBalance = null;
        } else {
            minimumBalance = in.readInt();
        }
        if (in.readByte() == 0) {
            finansialStatus = null;
        } else {
            finansialStatus = in.readInt();
        }
        alias = in.readString();
        currency_type = in.readString();
        icon_name = in.readString();
        icon_path = in.readString();
        imagePath = in.readString();
        imageName = in.readString();
        if (in.readByte() == 0) {
            limit = null;
        } else {
            limit = in.readLong();
        }
        limitString = in.readString();
        imageProductType = in.readString();
        isSelected = in.readByte() != 0;
        isOver = in.readByte() != 0;
        detailCcSofResponse = in.readParcelable(DetailCcSofResponse.class.getClassLoader());
        imageCardMiniName = in.readString();
        imageCardMiniPath = in.readString();
    }

    public static final Creator<AccountModel> CREATOR = new Creator<AccountModel>() {
        @Override
        public AccountModel createFromParcel(Parcel in) {
            return new AccountModel(in);
        }

        @Override
        public AccountModel[] newArray(int size) {
            return new AccountModel[size];
        }
    };

    public String getAcoount() {
        return acoount;
    }

    public void setAcoount(String acoount) {
        this.acoount = acoount;
    }

    public String getAcoountString() {
        return acoountString;
    }

    public void setAcoountString(String acoountString) {
        this.acoountString = acoountString;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getScCode() {
        return scCode;
    }

    public void setScCode(String scCode) {
        this.scCode = scCode;
    }

    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }

    public SaldoReponse getSaldoReponse() {
        return saldoReponse;
    }

    public void setSaldoReponse(SaldoReponse saldoReponse) {
        this.saldoReponse = saldoReponse;
    }

    public Integer getMinimumBalance() {
        return minimumBalance;
    }

    public void setMinimumBalance(Integer minimumBalance) {
        this.minimumBalance = minimumBalance;
    }

    public Integer getFinansialStatus() {
        return finansialStatus;
    }

    public void setFinansialStatus(Integer finansialStatus) {
        this.finansialStatus = finansialStatus;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getCurrency_type() {
        return currency_type;
    }

    public void setCurrency_type(String currency_type) {
        this.currency_type = currency_type;
    }

    public String getIcon_name() {
        return icon_name;
    }

    public void setIcon_name(String icon_name) {
        this.icon_name = icon_name;
    }

    public String getIcon_path() {
        return icon_path;
    }

    public void setIcon_path(String icon_path) {
        this.icon_path = icon_path;
    }

    public String getCardNumberString() {
        return cardNumberString;
    }

    public void setCardNumberString(String cardNumberString) {
        this.cardNumberString = cardNumberString;
    }

    public String getImagePath() {
        return imagePath;
    }

    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }

    public String getImageName() {
        return imageName;
    }

    public void setImageName(String imageName) {
        this.imageName = imageName;
    }

    public DetailCcSofResponse getDetailCcSofResponse() {
        return detailCcSofResponse;
    }

    public void setDetailCcSofResponse(DetailCcSofResponse detailCcSofResponse) {
        this.detailCcSofResponse = detailCcSofResponse;
    }

    public Long getLimit() {
        return limit;
    }

    public void setLimit(Long limit) {
        this.limit = limit;
    }

    public String getLimitString() {
        return limitString;
    }

    public void setLimitString(String limitString) {
        this.limitString = limitString;
    }

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }

    public boolean isOver() {
        return isOver;
    }

    public void setOver(boolean over) {
        isOver = over;
    }

    public String getImageProductType() {
        return imageProductType;
    }

    public String getNfcType() {
        return nfcType;
    }

    public void setNfcType(String nfcType) {
        this.nfcType = nfcType;
    }

    public void setImageProductType(String imageProductType) {
        this.imageProductType = imageProductType;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public Boolean getOnHold() {
        return onHold;
    }

    public void setOnHold(Boolean onHold) {
        this.onHold = onHold;
    }

    public Double getBalance() {
        return balance;
    }

    public void setBalance(Double balance) {
        this.balance = balance;
    }

    public String getBalanceString() {
        return balanceString;
    }

    public void setBalanceString(String balanceString) {
        this.balanceString = balanceString;
    }

    public String getImageCardMiniName() {
        return imageCardMiniName;
    }

    public String getImageCardMiniPath() {
        return imageCardMiniPath;
    }

    @Override
    public void writeToParcel(@NonNull Parcel dest, int flags) {
        dest.writeString(acoount);
        dest.writeString(acoountString);
        dest.writeString(name);
        dest.writeString(currency);
        dest.writeString(cardNumber);
        dest.writeString(cardNumberString);
        dest.writeString(productType);
        dest.writeString(accountType);
        dest.writeString(scCode);
        if (isDefault == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(isDefault);
        }
        if (minimumBalance == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(minimumBalance);
        }
        if (finansialStatus == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeInt(finansialStatus);
        }
        dest.writeString(alias);
        dest.writeString(currency_type);
        dest.writeString(icon_name);
        dest.writeString(icon_path);
        dest.writeString(imagePath);
        dest.writeString(imageName);
        if (limit == null) {
            dest.writeByte((byte) 0);
        } else {
            dest.writeByte((byte) 1);
            dest.writeLong(limit);
        }
        dest.writeString(limitString);
        dest.writeString(imageProductType);
        dest.writeByte((byte) (isSelected ? 1 : 0));
        dest.writeByte((byte) (isOver ? 1 : 0));
        dest.writeParcelable(detailCcSofResponse, flags);
        dest.writeString(imageCardMiniName);
        dest.writeString(imageCardMiniPath);
    }
}
package id.co.bri.brimo.data.api;


import com.chuckerteam.chucker.api.Chucker;

import io.reactivex.Observable;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.GET;
import retrofit2.http.Header;
import retrofit2.http.POST;
import retrofit2.http.Streaming;
import retrofit2.http.Url;


public interface ApiInterface {
    String headerApiKey = "X-API-KEY";
    String headerRandomKey = "X-RANDOM-KEY";
    String headerDeviceId = "X-DEVICE-ID";
    String headerDevice = "X-DEVICE";
    String headerId = "X-ID";
    String headerDklFlag = "X-DKL-FLAG";

    @FormUrlEncoded
    @POST
    Observable<String> login(
            @Url String url,
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id,
            @Header(Chucker.HEADER_KEY_SEQUENCE) String key
    );

    @FormUrlEncoded
    @POST("v2-otp-activation")
    Observable<String> userActivation(
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id,
            @Header(Chucker.HEADER_KEY_SEQUENCE) String key
    );

    @FormUrlEncoded
    @POST("v2-resend-otp-activation")
    Observable<String> resendActivation(
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id,
            @Header(Chucker.HEADER_KEY_SEQUENCE) String key
    );

    @FormUrlEncoded
    @POST("v1-registration-nds-magiclink-check")
    Observable<String> getMagicLinkRegisNds(
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id,
            @Header(Chucker.HEADER_KEY_SEQUENCE) String key
    );

    @FormUrlEncoded
    @POST(" v1-info-new-dynamic-screen")
    Observable<String> getBannerImageUrl(
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id,
            @Header(Chucker.HEADER_KEY_SEQUENCE) String key
    );

    @FormUrlEncoded
    @POST("v1-create-pin-alias")
    Observable<String> createPin(
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id,
            @Header(Chucker.HEADER_KEY_SEQUENCE) String key
    );


    @FormUrlEncoded
    @POST
    Observable<String> logout(
            @Url String url,
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id,
            @Header(Chucker.HEADER_KEY_SEQUENCE) String key
    );

    @FormUrlEncoded
    @POST("v1-saldo-dashboard")
    Observable<String> getSaldo(
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id,
            @Header(Chucker.HEADER_KEY_SEQUENCE) String key
    );


    @FormUrlEncoded
    @POST("v1-transfer-confirmation")
    Observable<String> getTransferConfirmation(
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id,
            @Header(Chucker.HEADER_KEY_SEQUENCE) String key
    );


    @FormUrlEncoded
    @POST("v1-transfer-pay")
    Observable<String> getTransferPaid(
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id,
            @Header(Chucker.HEADER_KEY_SEQUENCE) String key
    );

    @FormUrlEncoded
    @POST("v1-account-list-dplk")
    Observable<String> getRekeningDplk(
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id,
            @Header(Chucker.HEADER_KEY_SEQUENCE) String key
    );

    @FormUrlEncoded
    @POST("v5-user-account-list-all")
    Observable<String> getRekening(
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id,
            @Header(Chucker.HEADER_KEY_SEQUENCE) String key
    );


    @FormUrlEncoded
    @POST("v2-saldo-normal")
    Observable<String> getSaldoNormal(
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id,
            @Header(Chucker.HEADER_KEY_SEQUENCE) String key
    );

    @FormUrlEncoded
    @POST("v3-cc-sof-detail")
    Observable<String> getSaldoCc(
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id,
            @Header(Chucker.HEADER_KEY_SEQUENCE) String key
    );

    @FormUrlEncoded
    @POST("v1-saldo-fastmenu")
    Observable<String> getSaldoNormalFM(
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id,
            @Header(Chucker.HEADER_KEY_SEQUENCE) String key
    );


    @FormUrlEncoded
    @POST("v1-qna-mainpage")
    Observable<String> getPusatBantuan(
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id,
            @Header(Chucker.HEADER_KEY_SEQUENCE) String key
    );

    @FormUrlEncoded
    @POST
    Observable<String> getFormGeneral(
            @Url String url,
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id,
            @Header(Chucker.HEADER_KEY_SEQUENCE) String key
    );

    @FormUrlEncoded
    @POST
    Observable<String> getInquiryGeneral(
            @Url String url,
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id
    );


    @FormUrlEncoded
    @POST("v1-open-account-info-product")
    Observable<String> getJenisTabungan(
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id,
            @Header(Chucker.HEADER_KEY_SEQUENCE) String key
    );

    @FormUrlEncoded
    @POST("v1-activity-filter")
    Observable<String> getFilterAktivitas(
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id,
            @Header(Chucker.HEADER_KEY_SEQUENCE) String key
    );

    @FormUrlEncoded
    @POST
    Observable<String> checkPassword(
            @Url String url,
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id
    );


    /**
     * Method General request screen inquiry
     *
     * @param url         urlInformasi webservice
     * @param requestData parameter data yang dikirimkan
     * @param deviceId    ID Device generate by BRImo preference
     * @param device      tipe device
     * @param randomKey   randomn
     * @return
     */
    @FormUrlEncoded
    @POST
    Observable<String> getGeneralFastmenu(
            @Url String url,
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id,
            @Header(Chucker.HEADER_KEY_SEQUENCE) String key
    );


    /**
     * Method General request screen inquiry
     *
     * @param url         urlInformasi webservice
     * @param requestData parameter data yang dikirimkan
     * @param deviceId    ID Device generate by BRImo preference
     * @param device      tipe device
     * @param randomKey   randomn
     * @return
     */
    @FormUrlEncoded
    @POST
    Observable<String> getGeneralRequest(
            @Url String url,
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id,
            @Header(Chucker.HEADER_KEY_SEQUENCE) String key
    );

    /**
     * Method General request screen inquiry
     *
     * @param url         urlInformasi webservice
     * @param requestData parameter data yang dikirimkan
     * @param deviceId    ID Device generate by BRImo preference
     * @param device      tipe device
     * @param randomKey   randomn
     * @return
     */
    @FormUrlEncoded
    @POST
    Observable<String> getGeneralRequestC2(
            @Url String url,
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id,
            @Header(headerDklFlag) boolean dklFlag
    );

    /**
     * @param requestData String dari JSON Object BaseSubscribeTopicRequest
     * @param deviceId    ID Device generate by BRImo preference
     * @param device      tipe device
     * @param randomKey   random key
     * @return
     */

    @FormUrlEncoded
    @POST("v1-push-notification-update-token")
    Observable<String> getUpdateTokenFirebase(
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id,
            @Header(Chucker.HEADER_KEY_SEQUENCE) String key
    );

    /**
     * Method untuk subscribe topic firebase
     *
     * @param requestData String dari JSON Object SubscribeTopicRequest
     * @param deviceId    ID Device generate by BRImo preference
     * @param device      tipe device
     * @param randomKey   random key
     * @return
     */

    @FormUrlEncoded
    @POST("v1-push-notification-subscribe-topic")
    Observable<String> getSubscribeTopicFirebase(
            @Field("request") String requestData,
            @Header(headerDeviceId) String deviceId,
            @Header(headerDevice) String device,
            @Header(headerRandomKey) String randomKey,
            @Header(headerId) String id,
            @Header(Chucker.HEADER_KEY_SEQUENCE) String key
    );

    @GET
    Observable<String> getAuthMNV(
            @Url String url
    );
}
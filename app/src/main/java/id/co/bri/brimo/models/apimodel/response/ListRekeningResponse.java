package id.co.bri.brimo.models.apimodel.response;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

public class ListRekeningResponse {
    @SerializedName("account")
    @Expose
    private ArrayList<Account> account;
//    @SerializedName("popup_detail")
//    @Expose
//    private DetailPopUp detailPopUp;

    public ArrayList<Account> getAccount() {
        return account;
    }

    public void setAccount(ArrayList<Account> account) {
        this.account = account;
    }

    public static class Account {

        @SerializedName("account")
        @Expose
        private String account;
        @SerializedName("account_string")
        @Expose
        private String accountString;
        @SerializedName("name")
        @Expose
        private String name;
        @SerializedName("currency")
        @Expose
        private String currency;
        @SerializedName("card_number")
        @Expose
        private String cardNumber;
        @SerializedName("card_number_string")
        @Expose
        private String cardNumberString;
        @SerializedName("product_type")
        @Expose
        private String productType;
        @SerializedName("account_type")
        @Expose
        private String accountType;
        @SerializedName("sc_code")
        @Expose
        private String scCode;
        @SerializedName("finansial_status")
        @Expose
        private Integer finansialStatus;
        @SerializedName("default")
        @Expose
        private Integer _default;
        @SerializedName("alias")
        @Expose
        private String alias;
        @SerializedName("detail_type")
        @Expose
        private String detailType;
        @SerializedName("s3_dream_popup_show")
        @Expose
        private Boolean s3DreamPopupShow;
        @SerializedName("is_popup")
        @Expose
        private Boolean isPopUp;
        @SerializedName("image_path")
        @Expose
        private String productImageUrl;
        @SerializedName("image_card_mini_path")
        @Expose
        private String imageCardMiniPath;


        public Account(String account, String accountString, String name, String currency, String cardNumber,
                       String productType, String accountType, String scCode, Integer finansialStatus,
                       Integer _default, String alias, SaldoReponse saldoReponse, String detailType,
                       Boolean s3DreamPopupShow, String productImageUrl) {
            this.account = account;
            this.accountString = accountString;
            this.name = name;
            this.currency = currency;
            this.cardNumber = cardNumber;
            this.productType = productType;
            this.accountType = accountType;
            this.scCode = scCode;
            this.finansialStatus = finansialStatus;
            this._default = _default;
            this.alias = alias;
            this.saldoReponse = saldoReponse;
            this.detailType = detailType;
            this.s3DreamPopupShow = s3DreamPopupShow;
            this.productImageUrl = productImageUrl;
        }

        private SaldoReponse saldoReponse;

        public Account() {
        }

        public String getAccount() {
            return account;
        }

        public void setAccount(String account) {
            this.account = account;
        }

        public String getAccountString() {
            return accountString;
        }

        public void setAccountString(String accountString) {
            this.accountString = accountString;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }

        public String getCardNumber() {
            return cardNumber;
        }

        public void setCardNumber(String cardNumber) {
            this.cardNumber = cardNumber;
        }

        public String getProductType() {
            return productType;
        }

        public void setProductType(String productType) {
            this.productType = productType;
        }

        public String getAccountType() {
            return accountType;
        }

        public void setAccountType(String accountType) {
            this.accountType = accountType;
        }

        public String getScCode() {
            return scCode;
        }

        public void setScCode(String scCode) {
            this.scCode = scCode;
        }

        public Integer getFinansialStatus() {
            return finansialStatus;
        }

        public void setFinansialStatus(Integer finansialStatus) {
            this.finansialStatus = finansialStatus;
        }

        public Integer getDefault() {
            return _default;
        }

        public void setDefault(Integer _default) {
            this._default = _default;
        }

        public SaldoReponse getSaldoReponse() {
            return saldoReponse;
        }

        public void setSaldoReponse(SaldoReponse saldoReponse) {
            this.saldoReponse = saldoReponse;
        }

        public String getAlias() {
            return alias;
        }

        public void setAlias(String alias) {
            this.alias = alias;
        }

        public String getCardNumberString() {
            return cardNumberString;
        }

        public void setCardNumberString(String cardNumberString) {
            this.cardNumberString = cardNumberString;
        }

        public String getDetailType() {
            return detailType;
        }

        public void setDetailType(String detailType) {
            this.detailType = detailType;
        }

        public Boolean getS3DreamPopupShow() {
            return s3DreamPopupShow;
        }

        public void setS3DreamPopupShow(Boolean s3DreamPopupShow) {
            this.s3DreamPopupShow = s3DreamPopupShow;
        }

        public Boolean getPopUp() {
            return isPopUp;
        }

        public void setPopUp(Boolean popUp) {
            isPopUp = popUp;
        }

        public String getProductImageUrl() {
            return productImageUrl;
        }

        public String getImageCardMiniPath() {
            return imageCardMiniPath;
        }
    }

}

package id.co.bri.brimo.ui.activities.bukarekening

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.ReceiptDetailAdapter
import id.co.bri.brimo.databinding.ActivityReceiptTabunganActiviyBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.fragments.bukaRekening.BottomFragmentReceipt

class ReceiptTabunganActivity : BaseActivity(), View.OnClickListener {

    lateinit var binding: ActivityReceiptTabunganActiviyBinding
    var detailTransaksiRevampAdapter: ReceiptDetailAdapter? = null
    var bottomFragmentReceipt: BottomFragmentReceipt? = null

    companion object {
        var mPendingResponse: ReceiptRevampResponse? = null
        private var mfromCatatanAktivitas: Boolean = false
        private var mButtonText: String = ""
        private var mTrxType: String = ""

        fun launchIntent(
            caller: Activity,
            paymentResponse: ReceiptRevampResponse,
            fromCatatanAktivitas: Boolean,
            buttonText: String,
            trxType: String = ""
        ) {
            val intent = Intent(caller, ReceiptTabunganActivity::class.java)
            mPendingResponse = paymentResponse
            mfromCatatanAktivitas = fromCatatanAktivitas
            mButtonText = buttonText
            mTrxType = trxType
            caller.startActivityForResult(intent, Constant.REQ_BUKA_REKENING)
            caller.setResult(RESULT_OK, setResultReceipt(paymentResponse))
        }


        /**
         * Get return Intent untuk Dashboard activity
         *
         * @return
         */
        fun setResultReceipt(response: ReceiptRevampResponse): Intent? {
            val intentReturn = Intent()
            return try {
                if (response.titleImage != null) {
                    if (response.titleImage.contains("00")) {
                        intentReturn.putExtra(Constant.REQUEST_RECEIPT, true)
                        intentReturn
                    } else {
                        intentReturn.putExtra(Constant.REQUEST_RECEIPT, false)
                        intentReturn
                    }
                } else {
                    intentReturn.putExtra(Constant.REQUEST_RECEIPT, true)
                    intentReturn
                }
            } catch (e: Exception) {
                intentReturn.putExtra(Constant.REQUEST_RECEIPT, true)
                intentReturn
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityReceiptTabunganActiviyBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupView()
    }

    private fun setupView() {
        with(binding) {
            setStatusColorAndStatusBar(R.color.highlightColor, View.SYSTEM_UI_FLAG_VISIBLE);
            tvDetailTabungan.setOnClickListener(this@ReceiptTabunganActivity)
            btnRekeningBaru.setOnClickListener(this@ReceiptTabunganActivity)
            tvDetail.text = mPendingResponse!!.footer
            binding.animationView.playAnimation()
            binding.lottieView.playAnimation()

            tvTitleTime.text = mPendingResponse!!.transactionDate
            btnRekeningBaru.text = mButtonText
            if (mTrxType == Constant.PAYMENT_SUBMIT_REGIST_DPLK) {
                tvDetailTabungan.text = GeneralHelper.getString(R.string.txt_expand_title_open_dplk)
                tvDetail.text = GeneralHelper.getString(R.string.txt_desc_receipt_dplk)
            }

            GeneralHelper.loadIconTransaction(
                this@ReceiptTabunganActivity,
                mPendingResponse!!.transactionImage,
                "",
                ivProduct,
                0
            )

            rvDetailReceipt.setHasFixedSize(true)
            rvDetailReceipt.layoutManager = LinearLayoutManager(
                applicationContext, RecyclerView.VERTICAL, false
            )
            detailTransaksiRevampAdapter =
                ReceiptDetailAdapter(mPendingResponse!!.mainDataView, this@ReceiptTabunganActivity)
            rvDetailReceipt.adapter = detailTransaksiRevampAdapter

            GeneralHelper.setWebViewBackground(webview,"", mPendingResponse?.title, Color.TRANSPARENT)

        }

    }

    override fun onClick(p0: View?) {
        when (p0!!.id) {
            R.id.btn_rekening_baru -> onBackPressed()
            R.id.tvDetailTabungan -> {
                bottomFragmentReceipt = BottomFragmentReceipt.newInstance(mPendingResponse!!, mTrxType )
                bottomFragmentReceipt!!.show(supportFragmentManager, "")
            }
            else -> {}
        }
    }

    override fun onBackPressed() {
        if (mfromCatatanAktivitas) {
            super.onBackPressed()
        } else {
            val i = Intent()
            setResult(RESULT_FIRST_USER, i)
            finish()
        }
    }
}
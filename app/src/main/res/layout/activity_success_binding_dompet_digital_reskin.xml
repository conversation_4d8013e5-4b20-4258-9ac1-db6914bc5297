<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
		xmlns:card_view="http://schemas.android.com/apk/res-auto"
		xmlns:tools="http://schemas.android.com/tools"
		android:layout_width="match_parent"
		android:layout_height="match_parent"
		android:background="@drawable/background_main_page_ns">

	<androidx.coordinatorlayout.widget.CoordinatorLayout
			android:layout_width="match_parent"
			android:layout_height="match_parent">

<LinearLayout
		android:layout_width="match_parent"
		android:layout_height="wrap_content"
		android:orientation="vertical"
		android:layout_gravity="center_vertical"
		android:gravity="center"
		android:padding="@dimen/size_40dp"
		>
	<ImageView
			android:layout_width="@dimen/size_200dp"
			android:layout_height="@dimen/size_200dp"
			android:src="@drawable/ic_checklist_receipt_newskin"/>
	<TextView
			android:id="@+id/tv_binding_title"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginBottom="@dimen/size_8dp"
			android:text="@string/txt_berhasil_binding_e_wallet"
			android:textColor="@color/text_white_default_ns"
			android:fontFamily="@font/bri_digital_text_semibold"
			android:textSize="28sp"/>

		<LinearLayout
				android:id="@+id/ll_container"
				android:layout_width="match_parent"
				android:layout_height="match_parent"
				android:layout_gravity="center"
				android:orientation="horizontal"
				android:gravity="center_vertical"
				android:layout_marginVertical="@dimen/size_16dp"
				android:background="@drawable/bg_input_black_100_brimo_ns"
				android:padding="@dimen/size_16dp">

			<LinearLayout
					android:layout_width="@dimen/size_30dp"
					android:layout_height="@dimen/size_30dp"
					android:layout_marginEnd="@dimen/size_12dp"
					android:background="@drawable/round_icon_ns"
					android:layout_gravity="center_vertical"
					>

				<ImageView
						android:id="@+id/img_banner"
						android:layout_width="match_parent"
						android:layout_height="match_parent"
						android:scaleType="fitXY"
						tools:src="@drawable/ic_ewallet_gopay"/>

			</LinearLayout>

			<LinearLayout
					android:id="@+id/ll_detail"
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					android:layout_gravity="center_vertical"
					android:orientation="vertical">

				<TextView
						android:id="@+id/tv_title"
						style="@style/BodySmallText.DemiBold.Black"
						android:layout_width="match_parent"
						android:layout_height="wrap_content"
						android:textColor="@color/text_gray_default_ns"
						android:ellipsize="end"
						android:textSize="@dimen/size_14sp"
						tools:text="GoPay"/>
				<TextView
						android:id="@+id/tv_no_pelanggan"
						style="@style/BodySmallText.DemiBold.Black"
						android:layout_width="match_parent"
						android:layout_height="wrap_content"
						android:layout_marginTop="@dimen/size_4dp"
						android:ellipsize="end"
						android:textSize="@dimen/size_14sp"
						tools:text="08214136001"/>
			</LinearLayout>

		</LinearLayout>

	<TextView
			android:id="@+id/tv_binding_desc"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:textAlignment="center"
			android:textSize="@dimen/size_14sp"
			android:textColor="@color/text_white_default_ns"
			android:text="@string/txt_desc_sukses_hubungkan_e_wallet"/>

</LinearLayout>

	<!-- Bottom Button -->
	<LinearLayout
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:layout_gravity="bottom"
			android:layout_alignParentBottom="true"
			android:orientation="vertical"
			android:padding="16dp"
			android:elevation="8dp">

		<Button
				android:id="@+id/btnSubmit"
				android:layout_width="match_parent"
				android:layout_height="56dp"
				style="@style/CustomButtonStyle"
				android:text="@string/txt_btn_selesai"
				android:textSize="16sp"
				android:textStyle="bold"
				android:textAllCaps="false"
				android:background="@drawable/rounded_button_border_blue_ns"
				android:textColor="@color/text_brand_primary_ns"

				/>
	</LinearLayout>
	</androidx.coordinatorlayout.widget.CoordinatorLayout>


</FrameLayout>